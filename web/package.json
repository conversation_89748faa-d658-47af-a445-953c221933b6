{"name": "Blueprint AI", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "openapi": "openapi2ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\" --ignore-path .prettierignore"}, "dependencies": {"@ant-design/pro-components": "^2.8.6", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ant-design/x": "^1.0.5", "@antv/gpt-vis": "^0.4.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@icon-park/react": "^1.4.2", "@lobehub/icons": "^2.2.0", "@madzadev/audio-player": "^2.1.14", "@microlink/react-json-view": "^1.26.1", "@types/react-router-dom": "^5.3.3", "@uiw/codemirror-extensions-langs": "^4.23.12", "@uiw/codemirror-theme-github": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "antd": "^5.15.0", "antd-style": "^3.7.1", "axios": "^1.9.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "lucide-react": "^0.479.0", "markdown-it": "^14.1.0", "markdown-it-mermaid": "^0.2.5", "mermaid": "^11.6.0", "openai": "^4.86.1", "path-to-regexp": "^8.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-h5-audio-player": "^3.10.0-rc.1", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "react-router-dom": "^7.2.0", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-starry-night": "^2.2.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tailwind-merge": "^3.2.0", "tailwind-scrollbar-hide": "^2.0.0", "umi-request": "^1.4.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/highlight.js": "^10.1.0", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^22.13.8", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@umijs/openapi": "^1.13.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "sass-embedded": "^1.85.1", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-svgr": "^4.3.0"}}