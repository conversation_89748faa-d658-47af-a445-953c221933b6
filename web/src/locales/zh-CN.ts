export const translations = {
  menu: {
    welcome: '欢迎',
    assistant: '智能助理',
    assistant1: '助理',
    assistant2: '助理',
    ai_creation: {
      title: '创作',
      inspiration: '创意中心',
      article: '文章创作',
      story: '故事创作',
      image_generation: '图像生成',
      video_generation: '视频生成',
      audio_generation: '音频生成',
    },
    data_center: {
      title: '数据',
      knowledge_base: '知识库',
      knowledge_base_create: '创建知识库',
      knowledge_base_detail: '知识库详情',
    },
    settings: {
      title: '设置',
      model_service: '模型服务',
      vector_store: '向量存储',
      web_search: '网络搜索',
      general_settings: '常规设置',
      display_settings: '显示设置',
      shortcuts: '快捷方式',
    },
    help: {
      title: '帮助',
      ant_design: 'Ant Design 官网',
      ant_design_x: 'Ant Design X 官网',
    },
  },
  image_creation: {
    model: {
      label: '模型',
      placeholder: '请选择模型',
    },
    image_size: {
      label: '图像尺寸',
      tooltip: '生成图像的长度_宽度比。',
    },
    number_images: {
      label: '图像数量',
      tooltip: '要生成的图像数量。',
    },
    seed: {
      label: '种子值',
      placeholder: '请输入种子值',
      tooltip: '相同的种子值和提示词可以生成相似的图像。',
    },
    inference_steps: {
      label: '推理步数',
      tooltip: '执行推理步骤的数量。更多的步骤会产生更高的质量，但需要更长的时间。',
    },
    guidance_scale: {
      label: '引导系数',
      tooltip:
        '无分类器引导（Classifier Free Guidance）。用于控制模型在生成相关图像时与您提供的提示词之间的贴合程度。',
    },
    negative_prompt: {
      label: '反向提示词',
      placeholder: '请输入反向提示词',
      tooltip: '描述您不希望包含在图像中的内容。',
    },
    prompt: {
      label: '提示词',
      placeholder: '请输入提示词',
      tooltip: '描述您想在图像中看到的内容。',
      already_input: '已输入',
      characters: '字符',
    },
    message: {
      failed_generate_image: '生成图像失败',
      image_will_appear: '生成的图像将显示在这里',
    },
    generate: '立即生成',
  },
  loading_messages: {
    sparking_creativity: '激发创意中...',
    almost_there: '即将完成...',
    drawing_amazing: '绘制精彩内容...',
    inspiration_fly: '让灵感飞翔...',
    casting_magic: '施展魔法...',
    incubating_creativity: '孕育创意...',
    inspiration_flowing: '灵感涌动...',
    generating_art: '生成艺术...',
    painting_dreams: '绘制梦想...',
    colors_blooming: '色彩绽放...',
  },
  chat: {
    conversation: {
      new: '开启新对话',
      rename: '重命名',
      delete: '删除',
    },
    attachments: {
      drop: '拖拽文件到此处',
      upload: '上传文件',
      description: '点击或拖拽文件到此区域上传',
    },
  },
  provider_type: {
    all: '全部',
    cloud: '云服务',
    local: '私有化部署',
  },
  model_type: {
    llm: '大语言模型',
    image: '图像模型',
    audio: '音频模型',
    video: '视频模型',
    embedding: '嵌入模型',
    rerank: '重排模型',
  },
  model_feature: {
    vision: '视觉识别',
    reasoning: '深度思考',
    web_search: '联网搜索',
    tool_call: '工具调用',
    text_to_image: '文生图',
    image_to_image: '图生图',
    text_to_speech: '文本转语音',
    speech_recognition: '语音识别',
    voice_cloning: '声音复刻',
    text_to_video: '文本转视频',
    image_to_video: '图生视频',
  },
  select_model: '选择模型',
  model_service: {
    provider: {
      search: {
        placeholder: '搜索模型提供商',
      },
      filter: {
        tooltip: '筛选提供商',
        all: '全部',
        enabled: '仅启用',
        disabled: '仅未启用',
      },
      add: '添加提供商',
      base_url: {
        label: 'API 基础地址',
        placeholder: '请输入 API 基础地址',
      },
      api_key: {
        label: 'API 密钥',
        placeholder: '请输入 API 密钥',
      },
    },
    model: {
      search: {
        placeholder: '搜索模型名称或 ID',
      },
      add: '添加',
      add_title: '添加模型',
      update_title: '编辑模型',
      manage: '管理',
      id: {
        label: '模型 ID',
        placeholder: '例如：gpt-3.5-turbo',
      },
      name: {
        label: '模型名称',
        placeholder: '例如：3.5-Turbo',
      },
      type: {
        label: '模型类型',
        placeholder: '请选择模型类型',
      },
      features: {
        label: '模型能力',
      },
      group: {
        label: '分组',
        placeholder: '请选择或输入新增分组，例如：ChatGPT',
      },
    },
  },
}
