export const translations = {
  menu: {
    welcome: 'Welcome',
    assistant: 'AI Assistant',
    assistant1: 'AI Assistant V1',
    assistant2: 'AI Assistant V2',
    ai_creation: {
      title: 'AI Creation',
      inspiration: 'Inspiration Center',
      article: 'Article Creation',
      story: 'Story Creation',
      image_generation: 'Image Generation',
      video_generation: 'Video Generation',
      audio_generation: 'Audio Generation',
    },
    data_center: {
      title: 'Data Center',
      knowledge_base: 'Knowledge Base',
      knowledge_base_create: 'Create Knowledge Base',
      knowledge_base_detail: 'Knowledge Base Detail',
    },
    settings: {
      title: 'Settings',
      model_service: 'Model Service',
      vector_store: 'Vector Store',
      web_search: 'Web Search',
      general_settings: 'General Settings',
      display_settings: 'Display Settings',
      shortcuts: 'Shortcuts',
    },
    help: {
      title: 'Help Center',
      ant_design: 'Ant Design WebSite',
      ant_design_x: 'Ant Design X WebSite',
    },
  },
  // Image Generation Page
  image_creation: {
    model: {
      label: 'Model',
    },
    image_size: {
      label: 'Image Size',
      tooltip: 'Length-width ratio of the generated image.',
    },
    number_images: {
      label: 'Number of Images',
      tooltip: 'The number of images to generate.',
    },
    seed: {
      label: 'Seed',
      placeholder: 'Enter seed',
      tooltip: 'The same seed and prompt can produce similar images.',
    },
    inference_steps: {
      label: 'Inference Steps',
      tooltip:
        'The number of inference steps to perform. More steps produce higher quality but take longer.',
    },
    guidance_scale: {
      label: 'Guidance Scale',
      tooltip:
        'Classifier Free Guidance. How close you want the model to stick to your prompt when looking for a related image to show you.',
    },
    negative_prompt: {
      label: 'Negative Prompt',
      placeholder: 'Enter negative prompt',
      tooltip: "Describe what you don't want included in the image.",
    },
    prompt: {
      label: 'Prompt',
      placeholder: 'Enter your prompt',
      tooltip: 'Describe what you want to see in the image.',
      already_input: '',
      characters: 'characters',
    },
    message: {
      failed_generate_image: 'Failed to generate image',
      image_will_appear: 'Generated image will appear here',
    },
    generate: 'Generate',
  },
  loading_messages: {
    sparking_creativity: 'Sparking creativity...',
    almost_there: 'Almost there...',
    drawing_amazing: 'Drawing something amazing...',
    inspiration_fly: 'Let inspiration fly...',
    casting_magic: 'Casting magic...',
    incubating_creativity: 'Incubating creativity...',
    inspiration_flowing: 'Inspiration flowing...',
    generating_art: 'Generating art...',
    painting_dreams: 'Painting dreams...',
    colors_blooming: 'Colors blooming...',
  },
  chat: {
    conversation: {
      new: 'New Conversation',
      rename: 'Rename',
      delete: 'Delete',
    },
    attachments: {
      drop: 'Drop files here',
      upload: 'Upload files',
      description: 'Click or drag files to this area to upload',
    },
  },
  provider_type: {
    all: 'All',
    cloud: 'Cloud',
    local: 'Self-Host',
  },
  model_type: {
    llm: 'LLM',
    image: 'Image',
    audio: 'Audio',
    video: 'Video',
    embedding: 'Embedding',
    rerank: 'Rerank',
  },
  model_feature: {
    vision: 'Vision',
    reasoning: 'Deep Reasoning',
    web_search: 'Web Search',
    tool_call: 'Tool Call',
    text_to_image: 'Text To Image',
    image_to_image: 'Image To Image',
    text_to_speech: 'TTS',
    speech_recognition: 'ASR',
    voice_cloning: 'Voice Cloning',
    text_to_video: 'Text To Video',
    image_to_video: 'Image To Video',
  },
  select_model: 'Select Model',
  model_service: {
    provider: {
      search: {
        placeholder: 'Search model provider',
      },
      filter: {
        tooltip: 'Filter providers',
        all: 'All',
        enabled: 'Enabled Only',
        disabled: 'Disabled Only',
      },
      add: 'Add Provider',
      base_url: {
        label: 'API Base URL',
        placeholder: 'Enter API Base URL',
      },
      api_key: {
        label: 'API Key',
        placeholder: 'Enter API Key',
      },
    },
    model: {
      search: {
        placeholder: 'Search model name or ID',
      },
      add: 'Add',
      add_title: 'Add Model',
      manage: 'Manage',
      id: {
        label: 'Model ID',
        placeholder: 'e.g., gpt-3.5-turbo',
      },
      name: {
        label: 'Model Name',
        placeholder: 'e.g., GPT-3.5-Turbo',
      },
      type: {
        label: 'Model Type',
        placeholder: 'Enter model type',
      },
      features: {
        label: 'Model Features',
      },
      group: {
        label: 'Group',
        placeholder: 'e.g., OpenAI',
      },
    },
  },
}
