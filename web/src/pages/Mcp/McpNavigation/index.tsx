import React from 'react'

import { Tooltip } from 'antd'

const platforms = [
  {
    icon: 'https://mcp.ing/_next/image?url=%2Fimages%2Fclients%2Fcursor.png&w=64&q=75',
    name: 'Cursor MCP',
    url: 'https://cursor.directory/mcp',
    description: 'Browse MCPs or post a MCP to reach 250,000+ monthly active developers.',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/184127137?s=200&v=4',
    name: 'Cline MCP',
    url: 'https://cline.bot/mcp-marketplace',
    description: 'A curated collection of MCP servers that makes discovery and installation easy',
  },
  {
    icon: 'https://smithery.ai/logo.svg',
    name: '<PERSON><PERSON>',
    url: 'https://smithery.ai/',
    description:
      'Smithery is a platform to help developers find and ship services designed to communicate with AI agents.',
  },
  {
    icon: 'https://composio.dev/wp-content/uploads/2025/02/Fevicon-composio.png',
    name: 'Composio',
    url: 'https://mcp.composio.dev/',
    description:
      "Connect your Agent, LLMs & IDE's to 250+ fully managed and federated MCP server implementations",
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01vVn7g32134zNZEeAR_!!6000000006928-55-tps-24-24.svg',
    name: '阿里云百炼 MCP 广场',
    url: 'https://bailian.console.aliyun.com/?tab=mcp#/mcp-market',
    description: '连接智能，即点即用。探索阿里云百炼全周期 MCP 服务',
  },
  {
    icon: 'data:image/png;base64,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',
    name: '魔搭社区 MCP 广场',
    url: 'https://www.modelscope.cn/mcp',
    description: '聚合优质MCP资源，拓展模型智能边界',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/67365215?s=88&v=4',
    name: '火山引擎大模型生态广场',
    url: 'https://www.volcengine.com/mcp-marketplace',
    description: "工具一键直连，模型无缝衔接，探索与体验大模型丰富生态服务，轻松集成全面且易用的工具，提供企业级稳定、高效、安全的技术支持，释放模型潜力，赋能创新应用"
  },
  {
    icon: 'https://gips3.baidu.com/it/u=**********,626435656&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f300_315',
    name: '百度搜索开放平台 MCP Server',
    url: 'https://sai.baidu.com/mcp',
    description: '探索海量可用的 MCP Servers',
  },
  {
    icon: 'data:image/svg+xml;base64,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',
    name: 'Higress MCP Server',
    url: 'https://mcp.higress.ai/',
    description: '借助 Higress，瞬间即可将现有 API 转化为 Remote MCP Server，铺设 AI 与现实世界最短的连接路径',
  },
  {
    icon: 'https://7463-tcb-advanced-a656fc-1257967285.tcb.qcloud.la/weda-uploader/ea7adbf26fbfed25b2f79b118628b73b-logo.svg',
    name: '腾讯云开发 MCP Server',
    url: 'https://tcb.cloud.tencent.com/mcp-server',
    description:
      '企业级AI工具链即插即用，云原生架构一键部署，安全合规加速智能升级。标准化AI工具集成，连接多平台提升开发效率。',
  },
  {
    icon: 'data:image/png;base64,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',
    name: '讯飞星辰 Agent 平台 MCP Server',
    url: 'https://agent.xfyun.cn/store/plugin',
    description: '行业优质 MCP Server 支持，自定义 MCP Server 一键托管',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/171570339?s=88&v=4',
    name: 'ACI.DEV',
    url: 'https://www.aci.dev/tools',
    description: 'Connect 600+ Tools to power your AI agent projects. Discover, explore and use the latest AI tools to power your innovative projects',
  },
  {
    icon: 'https://mcp.so/favicon.ico',
    name: 'Mcp.so',
    url: 'https://mcp.so/',
    description: 'Find Awesome MCP Servers and Clients，The largest collection of MCP Servers.',
  },
  {
    icon: 'https://mcp.ing/images/svg/logo_dark.svg',
    name: 'MCP.ing',
    url: 'https://mcp.ing/',
    description: 'AI Client + MCP.ing = Magic',
  },
  {
    icon: 'https://glama.ai/pwa-icon.png',
    name: 'Glama',
    url: 'https://glama.ai/mcp/servers',
    description: 'Your #1 Platform for Discovering Every MCP Server, Clients, and more.',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/9919?s=200&v=4',
    name: 'Awesome-MCP-ZH',
    url: 'https://github.com/yzfly/Awesome-MCP-ZH',
    description: '一个专为中文用户打造的 MCP（模型上下文协议）资源合集',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/108313943?v=4',
    name: 'Awesome MCP Servers',
    url: 'https://github.com/punkpeye/awesome-mcp-servers/blob/main/README-zh.md',
    url_cn: 'https://gitcode.com/gh_mirrors/aweso/awesome-mcp-servers',
    description: '精选的优秀模型上下文协议 (MCP) 服务器列表',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/108313943?v=4',
    name: 'Awesome MCP Client',
    url: 'https://github.com/punkpeye/awesome-mcp-clients/',
    description: '精选的 Model Context Protocol (MCP) 客户端列表',
  },
  {
    icon: 'https://avatars.githubusercontent.com/u/195895956?s=200&v=4',
    name: 'Pink Pixel',
    url: 'https://pinkpixel.dev/projects',
    description:
      'AI Enthusiast & Creative Developer, Crafting Digital Experiences with Code & Creativity',
  },
  {
    icon: 'https://www.redditstatic.com/chat-web/images/welcome-6AUNLRD4.png',
    name: 'r/mcp 社区',
    url: 'https://www.reddit.com/r/mcp/',
    description: 'Reddit 频道',
  },
  {
    icon: 'https://www.toolrouter.ai/images/brand/logo_white_red_bg.png',
    name: 'ToolRouter',
    url: 'https://www.toolrouter.ai/mcp',
    description: 'Powering the next generation of AI tool management and integration.',
  },
  {
    icon: 'https://mcpservers.org/_next/image?url=%2Ficon.png&w=64&q=75',
    name: 'Awesome MCP Servers',
    url: 'https://mcpservers.org/',
    description: 'Awesome MCP Servers. A collection of servers for the Model Context Protocol.',
  },
  {
    icon: 'https://www.mcpoogle.com/_next/image?url=%2Fimages%2Finspector_mcpoogle.png&w=384&q=75',
    name: 'McPoogle',
    url: 'https://www.mcpoogle.com/',
    description: 'Search engine for MCP Servers and Tools',
  },
]
const McpNavigation: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="mx-auto bg-white rounded-xl p-4 flex flex-col">
        <div className="min-h-0 flex-1">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {platforms.map(platform => (
              <div
                key={platform.name}
                className="group relative bg-white rounded-lg p-4 border border-gray-200
            hover:border-gray-300 shadow-sm hover:shadow-md transition-all cursor-pointer"
                onClick={() => window.open(platform.url, '_blank')}
              >
                <Tooltip title={platform.url}>
                  <div className="flex items-start min-h-[40px]">
                    <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden mr-3">
                      <img
                        src={
                          platform.icon ||
                          'https://resources.modelscope.cn/mcp-covers/avatar/modelcontextprotocol.png'
                        }
                        className="w-full h-full object-contain"
                        alt={platform.name}
                      />
                    </div>
                    <div className="flex-1 flex flex-col gap-1">
                      <h3 className="font-medium text-gray-900 truncate">{platform.name}</h3>
                      <p className="text-gray-500 text-xs line-clamp-2 leading-5">
                        {platform.description}
                      </p>
                    </div>
                  </div>
                </Tooltip>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
export default McpNavigation
