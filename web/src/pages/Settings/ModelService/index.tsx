import React, { useCallback, useEffect, useState } from 'react'
import {
  SearchOutlined,
  FilterOutlined,
} from '@ant-design/icons'
import { useLanguage } from '@/locales/languagecontext.tsx'
import { listProvider, updateProvider, reorderProvider } from '@/api/modelProvider.ts'
import { deleteModel, listModel } from '@/api/model.ts'
import ModelManage from './components/ModelManage.tsx'
import ModelAddModal from './components/ModelAddModal.tsx'
import ModelEditModal from './components/ModelEditModal.tsx'
import { Model } from '@/types/model'

import type { DragEndEvent } from '@dnd-kit/core'
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
  arrayMove,
} from '@dnd-kit/sortable'
import { useNotification } from "@/hooks/useNotification.ts";
import ProviderGroup, { ProviderGroupItem } from "./components/ProviderGroup.tsx";
import ProviderConfig from "@/pages/Settings/ModelService/components/ProviderConfig.tsx";
import ModelListSection from "@/pages/Settings/ModelService/components/ModelListSection.tsx";
import { Button, Dropdown, Input, Radio, Tooltip } from 'antd'


const PROVIDER_FILTER_OPTIONS = [ 'all', 'enabled', 'disabled' ] as const
type ProviderFilterStatus = typeof PROVIDER_FILTER_OPTIONS[number]

const ModelService: React.FC = () => {
  const {t} = useLanguage()
  const {notify, contextHolder} = useNotification()
  const [ isLoading, setIsLoading ] = useState(false)

  // 提供商状态
  const [ providers, setProviders ] = useState<API.ProviderResponse[]>([])
  const [ activeProvider, setActiveProvider ] = useState<API.ProviderResponse>()
  const [ providerSearchKeyword, setProviderSearchKeyword ] = useState('')
  const [ providerFilterStatus, setProviderFilterStatus ] = useState<ProviderFilterStatus>('all')
  const [ expandedGroupKeys, setExpandedGroupKeys ] = useState<string[]>([ 'cloud', 'local' ])

  // 模型状态
  const [ models, setModels ] = useState<Model[]>([])
  const [ modelSearchKeyword, setModelSearchKeyword ] = useState('')
  const [ isModelManageModalOpen, setIsModelManageModalOpen ] = useState(false)
  const [ isModelConfigModalOpen, setIsModelConfigModalOpen ] = useState(false)
  const [ editingModel, setEditingModel ] = useState<Model | null>(null)

  // 初始化加载模型提供商列表
  useEffect(() => {
    handleFetchProviders()
  }, [ providerFilterStatus ])

  // 当活动提供商变化时加载模型
  useEffect(() => {
    if (activeProvider) {
      handleFetchModels()
    }
  }, [ activeProvider ])

  // 加载提供商列表
  const handleFetchProviders = useCallback(async () => {
    setIsLoading(true)
    try {
      const params: API.listProviderParams = {}
      if (providerFilterStatus === 'enabled') params.enabled = true
      if (providerFilterStatus === 'disabled') params.enabled = false

      const data = await listProvider(params)
      if (!data || !Array.isArray(data)) {
        return
      }
      setProviders(data)

      if (!activeProvider && data.length > 0) {
        const firstCloudProvider = data.find(p => p.type === 'cloud')
        if (firstCloudProvider) setActiveProvider(firstCloudProvider)
      }
    } catch (error) {
      console.log("获取模型提供商列表失败", error)
      notify.error('获取模型提供商列表失败')
    } finally {
      setIsLoading(false)
    }
  }, [ providerFilterStatus, activeProvider, notify ])

  // 更新提供商配置
  const handleUpdateProvider = useCallback(async (values: Partial<API.ProviderResponse>) => {
    if (!activeProvider) return
    setIsLoading(true)

    try {
      const updatedProvider = await updateProvider(
        {provider_id: activeProvider.id},
        values as API.ProviderUpdateRequest
      )
      setActiveProvider(prev => ({...prev!, ...updatedProvider}))
      await handleFetchProviders()
    } catch (error) {
      console.log('更新模型提供商失败', error)
      notify.error('更新模型提供商失败')
    } finally {
      setIsLoading(false)
    }
  }, [ activeProvider, handleFetchProviders, notify ])

  // 加载模型列表
  const handleFetchModels = useCallback(async () => {
    if (!activeProvider) return
    setIsLoading(true)

    try {
      const data = await listModel({provider_id: activeProvider.id})
      if (!data || !Array.isArray(data)) return
      const formattedModels: Model[] = data.map(model => ({
        id: model.id,
        modelId: model.model_id,
        modelName: model.name,
        providerId: model.provider_id,
        providerName: model.provider_name,
        group: model.group,
        type: model.type,
        features: model.features,
        tags: model.features,
        added: true,
      }))
      setModels(formattedModels)
    } catch (error) {
      console.log("获取模型列表失败", error)
      notify.error('获取模型列表失败')
    } finally {
      setIsLoading(false)
    }
  }, [ activeProvider, notify ])

  // 删除模型
  const handleDeleteModel = useCallback(async (model: Model) => {
    try {
      await deleteModel({model_id: model.id})
      notify.success('删除模型成功', model.modelName)
      handleFetchModels()
    } catch (error) {
      console.log("删除模型失败", error)
      notify.error('删除模型失败', model.modelName)
    }
  }, [ handleFetchModels, notify ])

  // 提供商分组数据
  const providerGroups: ProviderGroupItem[] = [
    {
      key: 'cloud',
      label: t('provider_type.cloud'),
      children: providers.filter(
        p => p.type === 'cloud' && p.name.toLowerCase().includes(providerSearchKeyword.toLowerCase())
      ),
    },
    {
      key: 'local',
      label: t('provider_type.local'),
      children: providers.filter(
        p => p.type === 'local' && p.name.toLowerCase().includes(providerSearchKeyword.toLowerCase())
      ),
    },
  ].filter(group => group.children.length > 0)

  // 提供商拖拽排序
  const sensors = useSensors(
    useSensor(PointerSensor, {activationConstraint: {distance: 1}})
  )

  const handleDragEnd = useCallback(async ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeId = active.id as string
      const overId = over?.id as string

      try {
        setProviders(prev => {
          const activeIndex = prev.findIndex(i => i.id === activeId)
          const overIndex = prev.findIndex(i => i.id === overId)
          return arrayMove(prev, activeIndex, overIndex)
        })

        await reorderProvider({provider_id: activeId, target_provider_id: overId})
      } catch (error) {
        console.log("更新提供商排序失败", error)
        notify.error('更新提供商排序失败')
        handleFetchProviders()
      }
    }
  }, [ handleFetchProviders, notify ])

  // 分组展开/折叠切换
  const handleToggleGroup = useCallback((key: string) => {
    setExpandedGroupKeys(prev =>
      prev.includes(key) ? prev.filter(k => k !== key) : [ ...prev, key ]
    )
  }, [])

  // 提供商选择
  const handleSelectProvider = useCallback((provider: API.ProviderResponse) => {
    setActiveProvider(provider)
  }, [])

  // 模型管理
  const handleOpenModelManage = useCallback(() => {
    setIsModelManageModalOpen(true)
  }, [])

  const handleCloseModelManage = useCallback(() => {
    setIsModelManageModalOpen(false)
  }, [])

  const handleOpenModelConfig = useCallback(() => {
    setIsModelConfigModalOpen(true)
  }, [])

  const handleCloseModelConfig = useCallback(() => {
    setIsModelConfigModalOpen(false)
    setEditingModel(null)
  }, [])

  const onDragEnd = async ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      // 获取拖动项和目标项的ID
      const activeId = active.id as string
      const overId = over?.id as string

      try {
        // 先更新UI状态，提供即时反馈
        setProviders(prev => {
          const activeIndex = prev.findIndex(i => i.id === activeId)
          const overIndex = prev.findIndex(i => i.id === overId)
          return arrayMove(prev, activeIndex, overIndex)
        })

        // 调用API更新排序
        await reorderProvider({provider_id: activeId, target_provider_id: overId})
        console.log('提供商排序已更新')

        // 可选：重新获取列表以确保UI与服务器同步
        // await fetchProviders();
      } catch (error) {
        console.error('更新提供商排序失败:', error)
        notify.error('更新提供商排序失败')
        // 如果失败，可以选择重新获取列表恢复原状态
        await handleFetchProviders()
      }
    }
  }

  return (
    <>
      {contextHolder}
      <div className="h-[calc(100vh-50px)] bg-white overflow-hidden rounded border-0 border-solid flex">
        {/* 侧边栏 */}
        <div className="w-[260px] bg-white p-4 h-full overflow-y-hidden">
          <div className="flex flex-col h-[calc(100vh-80px)]">
            <div className="flex items-center gap-3 mb-4">
              <Input
                placeholder={t('model_service.provider.search.placeholder')}
                prefix={<SearchOutlined className="text-gray-400"/>}
                onChange={e => setProviderSearchKeyword(e.target.value)}
                allowClear
              />
              <Dropdown
                menu={{
                  items: [ {
                    key: 'filter_options',
                    label: (
                      <Radio.Group
                        value={providerFilterStatus}
                        onChange={e => setProviderFilterStatus(e.target.value)}
                      >
                        <div className="flex flex-col gap-2 p-2">
                          {PROVIDER_FILTER_OPTIONS.map(option => (
                            <Radio key={option} value={option}>
                              {t(`model_service.provider.filter.${option}`)}
                            </Radio>
                          ))}
                        </div>
                      </Radio.Group>
                    ),
                  } ],
                }}
                trigger={[ 'click' ]}
                placement="bottomRight"
              >
                <Tooltip title={t('model_service.provider.filter.tooltip')}>
                  <Button
                    icon={<FilterOutlined/>}
                    type={providerFilterStatus !== 'all' ? 'primary' : 'default'}
                  />
                </Tooltip>
              </Dropdown>
            </div>

            {/* 提供商列表 */}
            <div className="flex flex-col flex-1 overflow-y-auto gap-3 provider-list">
              <DndContext
                sensors={sensors}
                modifiers={[ restrictToVerticalAxis ]}
                onDragEnd={handleDragEnd}
              >
                {providerGroups.map(group => (
                  <ProviderGroup
                    key={group.key}
                    group={group}
                    isExpanded={expandedGroupKeys.includes(group.key)}
                    activeProviderId={activeProvider?.id}
                    onToggle={handleToggleGroup}
                    onSelectProvider={handleSelectProvider}
                    onDragEnd={(e: DragEndEvent) => onDragEnd(e)}
                  />
                ))}
              </DndContext>
            </div>

            <Button className="mt-2.5">{t('model_service.provider.add')}</Button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="ml-2.5 p-6 bg-white h-full flex flex-col overflow-y-auto flex-1">
          {activeProvider && (
            <>
              <ProviderConfig
                provider={activeProvider}
                onUpdate={handleUpdateProvider}
                onCheckConnectivity={() => console.log('Connectivity check')}
              />

              <ModelListSection
                models={models}
                searchKeyword={modelSearchKeyword}
                onAddModel={handleOpenModelConfig}
                onManageModels={handleOpenModelManage}
                onEditModel={setEditingModel}
                onDeleteModel={handleDeleteModel}
                canManageModels={!!activeProvider.config?.support_models_endpoint}
                onSearchChange={setModelSearchKeyword}
              />
            </>
          )}
        </div>

        {/* 模型管理弹窗 */}
        <ModelManage
          open={isModelManageModalOpen}
          onCancel={handleCloseModelManage}
          providerId={activeProvider?.id || ''}
          name={activeProvider?.name || ''}
          apiKey={activeProvider?.api_key || ''}
          baseUrl={activeProvider?.base_url || ''}
          addedModelIdMapping={models.reduce<Record<string, string>>(
            (acc, m) => ({...acc, [m.modelId]: m.id}), {}
          )}
          onSuccess={handleFetchModels}
        />

        {/* 模型配置弹窗 */}
        {editingModel ? (
          <ModelEditModal
            open={isModelConfigModalOpen}
            onCancel={handleCloseModelConfig}
            providerId={activeProvider?.id || ''}
            groups={new Set<string>(models.map(m => m.group))}
            onSuccess={() => {
              handleFetchModels()
              handleCloseModelConfig()
            }}
            initialValues={{
              id: editingModel.id,
              modelId: editingModel.modelId,
              name: editingModel.modelName,
              type: editingModel.type,
              features: editingModel.features,
              group: editingModel.group,
            }}
          />
        ) : (
          <ModelAddModal
            open={isModelConfigModalOpen}
            onCancel={handleCloseModelConfig}
            providerId={activeProvider?.id || ''}
            groups={new Set<string>(models.map(m => m.group))}
            onSuccess={() => {
              handleFetchModels()
              handleCloseModelConfig()
            }}
          />
        )}
      </div>
    </>
  )
}

export default ModelService