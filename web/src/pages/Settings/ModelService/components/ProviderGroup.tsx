import React from "react";
import { CaretDownOutlined, CaretRightOutlined } from "@ant-design/icons";
import ProviderItem from "./ProviderItem";

import type { DragEndEvent } from '@dnd-kit/core'
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'

export type ProviderGroupItem = {
  key: string
  label: string
  children: API.ProviderResponse[]
}

// 提供商分组组件
interface ProviderGroupProps {
  group: ProviderGroupItem
  isExpanded: boolean
  activeProviderId?: string
  onToggle: (key: string) => void
  onSelectProvider: (provider: API.ProviderResponse) => void
  onDragEnd: (e: DragEndEvent) => void
}

const ProviderGroup: React.FC<ProviderGroupProps> = ({
                                                       group,
                                                       isExpanded,
                                                       activeProviderId,
                                                       onToggle,
                                                       onSelectProvider,
                                                       onDragEnd
                                                     }) => {

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    })
  )

  return (
    <div key={group.key}>
      <div
        className="flex items-center justify-between p-3 cursor-pointer rounded-md bg-gray-50"
        onClick={() => onToggle(group.key)}
      >
        <span className="font-medium">{group.label}</span>
        {isExpanded ? (
          <CaretDownOutlined className="text-gray-400"/>
        ) : (
          <CaretRightOutlined className="text-gray-400"/>
        )}
      </div>
      {isExpanded && (
        <DndContext
          sensors={sensors}
          modifiers={[ restrictToVerticalAxis ]}
          onDragEnd={onDragEnd}
        >
          <SortableContext
            items={group.children.map(provider => provider.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="ml-3 mb-2 mt-2 space-y-2">
              {group.children.map(provider => (
                <ProviderItem
                  key={provider.id}
                  provider={provider}
                  isActive={provider.id === activeProviderId}
                  onSelect={onSelectProvider}/>
              ))}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  )
}

export default ProviderGroup