import { useLanguage } from "@/locales/LanguageContext"
import { Model } from '@/types/model'
import ModelListCard from "./ModelListCard"
import { Button, Input, Tooltip } from "antd"
import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

// 模型列表区域组件
interface ModelListSectionProps {
  models: Model[]
  searchKeyword: string
  onAddModel: () => void
  onManageModels: () => void
  onEditModel: (model: Model) => void
  onDeleteModel: (model: Model) => void
  canManageModels: boolean
  onSearchChange: (keyword: string) => void
}

const ModelListSection: React.FC<ModelListSectionProps> = ({
                                                             models,
                                                             searchKeyword,
                                                             onAddModel,
                                                             onManageModels,
                                                             onEditModel,
                                                             onDeleteModel,
                                                             onSearchChange,
                                                             canManageModels,
                                                           }) => {
  const { t } = useLanguage()

  // 过滤模型
  const filteredModels = models.filter(model =>
    model.id.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    model.modelName.toLowerCase().includes(searchKeyword.toLowerCase())
  )

  // 分组模型
  const groupedModels = filteredModels.reduce<Record<string, Model[]>>((acc, model) => {
    const provider = model.group || 'Default'
    if (!acc[provider]) acc[provider] = []
    model.added = true
    acc[provider].push(model)
    return acc
  }, {})

  return (
    <div className="flex flex-col">
      <div className="flex mb-5">
        <Button icon={<PlusOutlined />} className="mr-2.5" onClick={onAddModel}>
          {t('model_service.model.add')}
        </Button>
        <Tooltip
          title={!canManageModels ? '当前模型提供商不支持通过接口获取支持的模型列表' : ''}
        >
          <Button
            type="primary"
            icon={<SettingOutlined />}
            disabled={!canManageModels}
            onClick={onManageModels}
          >
            {t('model_service.model.manage')}
          </Button>
        </Tooltip>

        <Input.Search
          placeholder={t('model_service.model.search.placeholder')}
          allowClear
          className="ml-4 w-[300px]"
          onChange={(e) => onSearchChange(e.target.value)}
          value={searchKeyword}
        />
      </div>

      <div className="flex flex-col gap-4">
        {Object.entries(groupedModels).map(([provider, models]) => (
          <ModelListCard
            key={provider}
            group={provider}
            provider={provider}
            models={models}
            onEditModel={onEditModel}
            onDeleteModel={onDeleteModel}
          />
        ))}
      </div>
    </div>
  )
}

export default ModelListSection