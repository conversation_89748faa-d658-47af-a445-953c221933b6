import React from 'react'

import { <PERSON><PERSON>, <PERSON>, Flex, List, Tag, Tooltip } from 'antd'
import { Atom, AudioLines, Eye, Image, Video } from 'lucide-react'
import { PlusCircleOutlined, MinusCircleOutlined, SettingOutlined } from '@ant-design/icons'
import { getModelIcon } from '@/utils/model'
import { Model } from '@/types/model'

type ModelListCardProps = {
  group: string
  provider: string
  models: Model[]
  onAddModel?: (model: Model) => void
  onDeleteModel?: (model: Model) => void
  onEditModel?: (model: Model) => void
}

const ModelListCard: React.FC<ModelListCardProps> = ({
  group,
  provider,
  models,
  onAddModel,
  onDeleteModel,
  onEditModel,
}) => {
  return (
    <>
      <Card key={group} title={group} size="small">
        <List
          size="small"
          split={false}
          dataSource={models}
          renderItem={item => (
            <List.Item key={item.id || item.modelId}>
              {/* 左侧基本信息 */}
              <Flex vertical={false} style={{ width: '100%' }}>
                <Flex vertical={false} align="center" style={{ flex: 1, width: '100%' }}>
                  {getModelIcon(["github", "fireworksai"].includes(provider) ? (item.modelName) : (item.modelId || item.id), 24, provider)}
                  <span style={{ marginLeft: 10 }}>{item.modelName}</span>
                  <Flex vertical={false} align="center" gap={5} className="ml-2">
                    {item.type === 'embedding' && (
                      <Tooltip title="向量嵌入模型">
                        <Tag color="#f97316">Embedding</Tag>
                      </Tooltip>
                    )}
                    {item.type === 'reranker' && (
                      <Tooltip title="重排序模型">
                        <Tag color="#0ea5e9">Reranker</Tag>
                      </Tooltip>
                    )}
                    {item.type === 'image' && (
                      <Tooltip title="图像模型">
                        <Image size="1em" color="#ec4899" />
                      </Tooltip>
                    )}
                    {item.type === 'video' && (
                      <Tooltip title="视频模型">
                        <Video size="1em" color="#f59e0b" />
                      </Tooltip>
                    )}
                    {item.type === 'audio' && (
                      <Tooltip title="音频模型">
                        <AudioLines size="1em" color="#06b6d4" />
                      </Tooltip>
                    )}
                    {item.features?.includes('reasoning') && (
                      <Tooltip title="该支持深度思考">
                        <Atom size="1em" color="#8b5cf6" />
                      </Tooltip>
                    )}
                    {item.features?.includes('vision') && (
                      <Tooltip title="该支持视觉识别">
                        <Eye size="1em" color="#10b981" />
                      </Tooltip>
                    )}
                  </Flex>
                </Flex>

                {/* 右侧操作：添加、删除、编辑 */}
                <div className="flex items-center gap-1">
                  {/* 编辑 */}
                  {onEditModel && (
                    <Button
                      type="text"
                      icon={<SettingOutlined />}
                      onClick={() => {
                        onEditModel(item)
                      }}
                    />
                  )}
                  {/* 添加 */}
                  {!item.added && onAddModel && (
                    <Button
                      type="text"
                      icon={<PlusCircleOutlined />}
                      onClick={() => {
                        onAddModel(item)
                      }}
                    />
                  )}
                  {/* 删除 */}
                  {item.added && onDeleteModel && (
                    <Button
                      type="text"
                      icon={<MinusCircleOutlined style={{ color: 'red' }} />}
                      onClick={() => {
                        onDeleteModel(item)
                      }}
                    />
                  )}
                </div>
              </Flex>
            </List.Item>
          )}
        />
      </Card>
    </>
  )
}

export default ModelListCard
