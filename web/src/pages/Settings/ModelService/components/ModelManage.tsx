import React, { useEffect, useState } from 'react'
import { Modal, Input, Radio, Flex, notification } from 'antd'

const {Search} = Input
import { isAudioModel, isImageModel, isRerankModel, isVideoModel } from '@/utils/model.tsx'
import axios from 'axios'
import { addModel, deleteModel } from '@/api/model.ts'
import { isEmbeddingModel, isVisionModel, isReasoningModel } from '@/utils/model.tsx'
import ModelListCard from '@/pages/Settings/ModelService/components/ModelListCard.tsx'
import { Model } from '@/types/model'

interface ModelManageProps {
  open: boolean
  onCancel: () => void
  providerId: string
  name: string
  apiKey: string
  baseUrl: string
  addedModelIdMapping: Record<string, string> // key 为 modelId，value 为 id
  onSuccess?: () => void
}

const determineModelNameAndGroup = (model: Model) => {
  if (model.modelId.includes('/')) {
    // 例如：THUDM/glm-4-9b-chat，group 为 THUDM，name 为 glm-4-9b-chat
    const [ providerPart, namePart ] = model.modelId.split('/')
    model.group = providerPart.charAt(0).toUpperCase() + providerPart.slice(1)
    model.modelName = namePart
  } else if (model.modelId.includes('-')) {
    // 例如：glm-4-9b-chat，则 name 为 glm-4-9b-chat
    model.modelName = model.modelId
    model.group = determineGroupByModelName(model.modelName)
  } else {
    model.group = 'Default'
    model.modelName = model.modelId
  }
}

const determineGroupByModelName = (modelName: string) => {
  const modelNameParts = modelName.split('-')
  // 如果有版本号，则 group 为 modelId 的第一个单词，name 为 modelId 的第二个单词
  if (modelNameParts.length >= 2) {
    const versionRegex = /-(\d+(\.\d+)?[bB]?)$/
    if (versionRegex.test(`-${modelNameParts[1]}`)) {
      return modelNameParts[0].charAt(0).toUpperCase() + modelNameParts[0].slice(1)
    } else {
      return modelNameParts[0].charAt(0).toUpperCase() + modelNameParts[0].slice(1) + '-' + modelNameParts[1]
    }
  } else {
    return modelNameParts[0].charAt(0).toUpperCase() + modelNameParts[0].slice(1)
  }
}


const determineModelTypeAndFeature = (model: Model) => {
  // console.log(model)
  if (!model) {
    return
  }
  if (isVideoModel(model)) {
    model.type = 'video'
  } else if (isAudioModel(model)) {
    model.type = 'audio'
  } else if (isRerankModel(model)) {
    model.type = 'reranker'
  } else if (isEmbeddingModel(model)) {
    model.type = 'embedding'
  } else if (isImageModel(model)) {
    model.type = 'image'
  } else {
    model.type = 'llm'
  }

  const features: string[] = []
  if (isVisionModel(model)) {
    features.push('vision')
  }

  if (isReasoningModel(model)) {
    features.push('reasoning')
  }
  model.features = features

  const tags: string[] = []
  tags.push(model.type)
  if (model.features?.length > 0) {
    tags.push(...model.features)
  }
  model.tags = tags
}

type NotificationType = 'success' | 'info' | 'warning' | 'error'

const ModelManage: React.FC<ModelManageProps> = ({
                                                   open,
                                                   onCancel,
                                                   providerId,
                                                   name,
                                                   apiKey,
                                                   baseUrl,
                                                   addedModelIdMapping,
                                                   onSuccess,
                                                 }) => {
  const [ notifyApi, contextHolder ] = notification.useNotification()
  const [ models, setModels ] = useState<Model[]>([])
  const [ loading, setLoading ] = useState(false)
  const [ searchText, setSearchText ] = useState('')
  const [ selectedType, setSelectedType ] = useState('all')
  const [ types, setTypes ] = useState<string[]>([
    'all',
    'reasoning',
    'vision',
    'video',
    'audio',
    'music',
    'embedding',
    'reranker',
  ])

  const notify = (type: NotificationType, title: string, description?: string) => {
    notifyApi[type]({
      message: title,
      description: description || '',
      duration: 2,
    })
  }

  // 打开时，根据填写的 API 地址及密钥请求模型列表
  useEffect(() => {
    if (open && baseUrl) {
      const fetchModels = async () => {
        setLoading(true)
        try {
          const url = baseUrl.replace(/\/$/, '')
          const response = await axios.get(`${url}/models`, {
            headers: {
              Authorization: `Bearer ${apiKey}`,
            },
          })
          if (response.data) {
            const models = response.data?.data ? response.data.data : response.data
            console.log('模型提供商：' + providerId + ',提供如下模型：', models)
            if (providerId == 'github') {
              formattedModelForGithub(models)
            } else if (providerId == "fireworksai") {
              formattedModelForFireworks(models)
            } else {
              formattedModel(models)
            }
          }
        } catch (error) {
          notify('error', '获取模型列表失败')
          console.error('获取模型列表失败:', error)
        } finally {
          setLoading(false)
        }
      }

      fetchModels()
    }
  }, [ open, apiKey, baseUrl ])

  const formattedModel = (models: any[]) => {
    const formattedModels: Model[] = []
    let tags = new Set<string>()
    models.forEach((model: Model) => {
      const newModel: Model = {...model, modelId: model.id}
      determineModelNameAndGroup(newModel)
      determineModelTypeAndFeature(newModel)
      newModel.added = newModel.modelId in addedModelIdMapping
      formattedModels.push(newModel)
      tags = new Set([ ...tags, ...newModel.tags ])
    })

    tags.delete('llm')
    setModels(formattedModels)
    setTypes([ 'all', ...tags ])
  }

  const formattedModelForGithub = (models: any[]) => {
    const formattedModels: Model[] = []
    let tags = new Set<string>()
    models.forEach((model: any) => {
      const newModel: Model = {...model, modelId: model.id}
      newModel.modelName = model?.name
      newModel.group = model?.model_family.charAt(0).toUpperCase() + model?.model_family.slice(1)
      determineModelTypeAndFeature(newModel)
      newModel.added = newModel.modelId in addedModelIdMapping
      formattedModels.push(newModel)
      tags = new Set([ ...tags, ...newModel.tags ])
    })

    tags.delete('llm')
    setModels(formattedModels)
    setTypes([ 'all', ...tags ])
  }

  const formattedModelForFireworks = (models: any[]) => {
    const formattedModels: Model[] = []
    let tags = new Set<string>()
    models.forEach((model: any) => {
      const newModel: Model = {...model, modelId: model.id}
      newModel.modelName = model.id.split('/').pop()
      newModel.group = determineGroupByModelName(newModel.modelName)
      determineModelTypeAndFeature(newModel)
      newModel.added = newModel.modelId in addedModelIdMapping
      formattedModels.push(newModel)
      tags = new Set([ ...tags, ...newModel.tags ])
    })

    tags.delete('llm')
    setModels(formattedModels)
    setTypes([ 'all', ...tags ])
  }

  // 关闭时，清空模型列表
  useEffect(() => {
    if (!open) {
      setModels([])
    }
  }, [ open ])

  // 根据类型及关键词过滤模型列表
  const filteredModels: Model[] = models.filter(model => {
    const matchesSearch =
      model.id.toLowerCase().includes(searchText.toLowerCase()) ||
      model.modelName.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === 'all' || model.tags.includes(selectedType)
    return matchesSearch && matchesType
  })

  // 根据提供商分组模型
  const groupedModels = filteredModels.reduce<Record<string, Model[]>>((acc, model) => {
    const group = model.group || 'Default'
    if (!acc[group]) acc[group] = []
    acc[group].push(model)
    return acc
  }, {})

  // 添加模型
  const handleAddModel = async (model: Model) => {
    try {
      await addModel({
        provider_id: providerId,
        model_id: model.modelId,
        name: model.modelName,
        type: model.type,
        group: model.group,
        features: model.features,
      })
      notify('success', '添加模型成功', model.modelName)
      model.added = true
      setModels([ ...models ])
      onSuccess?.()
    } catch (error) {
      console.error('添加模型失败:', error)
      notify('error', '添加模型失败', model.modelName)
    }
  }

  // 删除模型
  const handleDeleteModel = async (model: Model) => {
    try {
      await deleteModel({
        model_id: addedModelIdMapping[model.modelId],
      })
      notify('success', '删除模型成功', model.modelName)
      model.added = false
      setModels([ ...models ])
      onSuccess?.()
    } catch (error) {
      console.error('删除模型失败:', error)
      notify('error', '删除模型失败', model.modelName)
    }
  }

  return (
    <>
      {contextHolder}
      <Modal
        title={name + ' Models'}
        open={open}
        onCancel={onCancel}
        footer={null}
        width={800}
        loading={loading}
      >
        <Flex vertical align="center" style={{marginTop: '25px'}}>
          {/* 模型类型-快捷筛选 */}
          <Radio.Group
            defaultValue="all"
            buttonStyle="solid"
            onChange={e => setSelectedType(e.target.value)}
          >
            {Array.from(types).map(type => (
              <Radio.Button key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Radio.Button>
            ))}
          </Radio.Group>
          {/* 搜索框 */}
          <Search
            placeholder="Search model id or name"
            allowClear
            style={{margin: '15px 0'}}
            onChange={e => setSearchText(e.target.value)}
            value={searchText}
          />
        </Flex>
        {/* 模型列表 */}
        <Flex vertical style={{height: '600px', overflowY: 'auto'}} gap={16}>
          {Object.entries(groupedModels).map(([ group, models ]) => (
            <ModelListCard
              key={group}
              group={group}
              provider={providerId}
              models={models}
              onAddModel={handleAddModel}
              onDeleteModel={handleDeleteModel}
            />
          ))}
        </Flex>
      </Modal>
    </>
  )
}

export default ModelManage
