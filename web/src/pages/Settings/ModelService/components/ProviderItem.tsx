// 提供商列表项组件
import { useSortable } from "@dnd-kit/sortable";
import { getProviderIcon } from "@/utils/model.tsx";
import React from "react";
import { CSS } from "@dnd-kit/utilities";

interface ProviderItemProps {
  provider: API.ProviderResponse
  isActive: boolean
  onSelect: (provider: API.ProviderResponse) => void
}

const ProviderItem: React.FC<ProviderItemProps> = ({ provider, isActive, onSelect }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: provider.id,
  })

  const style: React.CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: 'move',
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`flex items-center p-2 cursor-pointer rounded-md mb-1 ${
        isActive ? 'bg-gray-100' : ''
      }`}
      onClick={() => onSelect(provider)}
    >
      {getProviderIcon(provider.id, 24)}
      <span className={`ml-3 ${isActive ? 'text-gray-900' : 'text-gray-600'}`}>
        {provider.name}
      </span>
      {provider.enabled && <span className="text-[#52c41a] text-[9px] ml-auto">●</span>}
    </div>
  )
}

export default ProviderItem