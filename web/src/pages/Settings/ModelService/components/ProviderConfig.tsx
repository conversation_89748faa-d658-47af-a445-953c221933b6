import { useLanguage } from "@/locales/LanguageContext"
import React, { useCallback } from "react"
import { Button, Divider, Input, Switch, Tooltip } from "antd";
import { ExportOutlined } from "@ant-design/icons";

// 提供商配置表单组件
interface ProviderConfigFormProps {
  provider: API.ProviderResponse
  onUpdate: (values: Partial<API.ProviderResponse>) => void
  onCheckConnectivity: () => void
}

const ProviderConfigForm: React.FC<ProviderConfigFormProps> = ({
                                                                 provider,
                                                                 onUpdate,
                                                                 onCheckConnectivity,
                                                               }) => {
  const {t} = useLanguage()

  const handleChange = useCallback((field: keyof API.ProviderResponse, value: any) => {
    onUpdate({[field]: value})
  }, [ onUpdate ])

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <h4 className="text-xl font-medium">{provider.name}</h4>
          {provider.config?.open_platform_url && (
            <Tooltip title="前往官网">
              <Button
                size="small"
                icon={<ExportOutlined size={5}/>}
                type="text"
                className="ml-2"
                href={provider.config.open_platform_url}
                target="_blank"
                rel="noopener noreferrer"
              />
            </Tooltip>
          )}
        </div>
        <Switch
          checked={provider.enabled}
          onChange={checked => handleChange('enabled', checked)}
        />
      </div>
      <Divider/>
      <div className="flex flex-col mb-6 gap-4">
        {/* API 地址 */}
        <div className="flex flex-col gap-2">
          <div>{t('model_service.provider.base_url.label')}</div>
          <Input
            value={provider.base_url || ''}
            placeholder={t('model_service.provider.base_url.placeholder')}
            onChange={(e) => handleChange('base_url', e.target.value)}
          />
        </div>

        {/* API 密钥 */}
        <div className="flex flex-col gap-2">
          <div>{t('model_service.provider.api_key.label')}</div>
          <div className="flex gap-2">
            <Input.Password
              value={provider.api_key || ''}
              placeholder={t('model_service.provider.api_key.placeholder')}
              onChange={(e) => handleChange('api_key', e.target.value)}
            />
            <Button type="primary" onClick={onCheckConnectivity}>
              Check
            </Button>
          </div>
          {provider.config?.get_api_key_url && (
            <a
              href={provider.config.get_api_key_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs"
            >
              点击此处获取 API 密钥
            </a>
          )}
        </div>
      </div>
    </>

  )
}

export default ProviderConfigForm
