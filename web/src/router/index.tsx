import {
  AppstoreOutlined,
  BulbOutlined,
  ApiOutlined,
  ExperimentOutlined,
  ControlOutlined,
} from '@ant-design/icons'
import { match } from 'path-to-regexp'
import Chat from '@/pages/Chat'
import AIInspiration from '@/pages/Creation/AIInspiration'
import ArticleCreation from '@/pages/Creation/ArticleCreation'
import StoryCreation from '@/pages/Creation/StoryCreation'
import ImageGeneration from '@/pages/Creation/ImageGeneration'
import AudioGeneration from '@/pages/Creation/AudioGeneration'
import VideoGeneration from '@/pages/Creation/VideoGeneration'
import KnowledgeBase from '@/pages/Resource/KnowledgeBase'
import ModelService from '@/pages/Settings/ModelService'
import VectorStore from '@/pages/Settings/VectorStore'
import AppMarket from '@/pages/App/AppMarket'
import Prompt from '@/pages/Resource/Prompt'
import SearchEngine from '@/pages/Settings/SearchEngine'
import DefaultModel from '@/pages/Settings/DefaultModel'
import McpServer from '@/pages/Experiment/McpServer'
import { AppBuilder } from '@/pages/App/AppBuilder'
import MyApp from '@/pages/App/MyApp'
import { Bot } from 'lucide-react'
import McpMarket from '@/pages/Mcp/McpMarket'
import McpDetail from '@/pages/Mcp/McpDetail'
import McpEditor from '@/pages/Mcp/McpEditor'
import McpNavigation from '@/pages/Mcp/McpNavigation'

export interface Route {
  path: string
  name: string
  fullName?: string
  icon?: React.ReactNode
  element?: React.ReactNode
  routes?: Route[]
  isStandalone?: boolean
  hidden?: boolean
}

export const getMainNavRoutes = (): Route[] => {
  const navMenus = allNavMenus.map(menu => {
    return {
      path: menu.path,
      icon: menu.icon,
      name: menu.name,
      fullName: menu.fullName,
      element: menu.element,
    }
  })
  // console.log('获取主菜单', navMenus)
  return navMenus
}

export const getCurrentRoutes = (path: string): Route[] => {
  // console.log('获取当前路由', path)
  const navMenus = allNavMenus.filter(menus => menus.path == path)
  return navMenus[0]?.routes || [navMenus[0]]
}

export const getSubNavRoutes = (path: string): Route[] => {
  // console.log('获取子菜单', path)
  const navMenus = allNavMenus.filter(menus => menus.path == path)
  return navMenus[0]?.routes || []
}

export const findRouteElement = (routes: Route[], path: string): React.ReactNode | null => {
  for (const route of routes) {
    // 处理精确匹配
    if (route.path === path) {
      return route.element
    }

    // 处理动态路由参数匹配
    if (route.path && route.path.includes(':')) {
      const routePathSegments = route.path.split('/')
      const currentPathSegments = path.split('/')

      if (routePathSegments.length === currentPathSegments.length) {
        let isMatch = true

        for (let i = 0; i < routePathSegments.length; i++) {
          // 如果是参数部分（以:开头）或者路径段相同，则继续匹配
          if (
            routePathSegments[i].startsWith(':') ||
            routePathSegments[i] === currentPathSegments[i]
          ) {
            continue
          } else {
            isMatch = false
            break
          }
        }

        if (isMatch) {
          return route.element
        }
      }
    }

    // 递归检查子路由
    if (route.routes) {
      const element = findRouteElement(route.routes, path)
      if (element) {
        return element
      }
    }
  }
  return null
}

const allNavMenus: Route[] = [
  {
    path: '/assistant',
    icon: <Bot />,
    name: '助理',
    fullName: '智能助理',
    element: <Chat />,
  },
  {
    path: '/creation',
    icon: <BulbOutlined />,
    name: '创作',
    fullName: '创作工坊',
    routes: [
      {
        name: '创意广场',
        path: '/creation/inspiration',
        element: <AIInspiration />,
      },
      {
        name: '图像生成',
        path: '/creation/image',
        element: <ImageGeneration />,
      },
      {
        name: '视频生成',
        path: '/creation/video',
        element: <VideoGeneration />,
      },
      {
        name: '音频生成',
        path: '/creation/audio',
        element: <AudioGeneration />,
      },
      {
        name: '文章创作',
        path: '/creation/article',
        element: <ArticleCreation />,
      },
      {
        name: '故事创作',
        path: '/creation/story',
        element: <StoryCreation />,
      },
      {
        name: '数字分身',
        path: '/creation/dhr',
        element: <div />,
      },
    ],
  },
  {
    path: '/app',
    icon: <AppstoreOutlined />,
    name: '应用',
    fullName: '应用中心',
    routes: [
      {
        name: '应用广场',
        path: '/app/market',
        element: <AppMarket />,
      },
      {
        name: '应用编排',
        path: '/app/builder/:id',
        isStandalone: true,
        hidden: true,
        element: <AppBuilder />,
      },
      {
        name: '我的应用',
        path: '/app/mine',
        element: <MyApp />,
      },
      {
        name: '应用插件',
        path: '/app/plugin',
        element: <div />,
      },
      {
        name: 'MCP 导航',
        path: '/app/mcp/navigation',
        element: <McpNavigation />,
      },
      {
        name: 'MCP 广场',
        path: '/app/mcp/market',
        element: <McpMarket />,
      },
      {
        name: 'MCP 详情',
        path: '/app/mcp/:id',
        hidden: true,
        element: <McpDetail />,
      },
      {
        name: 'MCP 收录',
        path: '/app/mcp/editor',
        element: <McpEditor />,
      },
    ],
  },
  {
    path: '/resource',
    icon: <ApiOutlined />,
    name: '资源',
    fullName: '资源中心',
    routes: [
      {
        name: '知识库',
        path: '/resource/knowledge-base',
        element: <KnowledgeBase />,
      },
      {
        name: '提示词',
        path: '/resource/prompt',
        element: <Prompt />,
      },
      {
        name: '数据集',
        path: '/resource/dataset',
        element: <div />,
      },
      {
        name: '数据库',
        path: '/resource/database',
        element: <div />,
      },
    ],
  },
  {
    path: '/experiment',
    icon: <ExperimentOutlined />,
    name: '实验',
    fullName: '前沿实验室',
    routes: [
      {
        name: 'MCP Server',
        path: '/experiment/mcp-server',
        element: <McpServer />,
      },
      {
        name: '深度研究',
        path: '/experiment/deep-research',
        element: <div />,
      },
      {
        name: '智能问数',
        path: '/experiment/chat-with-data',
        element: <div />,
      },
      {
        name: 'Compute Use',
        path: '/experiment/compute-use',
        element: <div />,
      },
    ],
  },
  {
    path: '/settings',
    icon: <ControlOutlined />,
    name: '设置',
    fullName: '设置中心',
    routes: [
      {
        name: '模型服务',
        path: '/settings/model',
        element: <ModelService />,
      },
      {
        name: '默认模型',
        path: '/settings/default-model',
        element: <DefaultModel />,
      },
      {
        name: '搜索引擎',
        path: '/settings/search-engine',
        element: <SearchEngine />,
      },
      {
        name: '向量存储',
        path: '/settings/vector-store',
        element: <VectorStore />,
      },
    ],
  },
]

// 递归展开所有 Route（只执行一次）
const flattenRoutes = (routes: Route[]): Route[] => {
  const result: Route[] = []

  const walk = (list: Route[]) => {
    list.forEach(route => {
      result.push(route)
      if (route.routes) {
        walk(route.routes)
      }
    })
  }

  walk(routes)
  return result
}

// 缓存平铺后的路由列表
export const flatRouteList: Route[] = flattenRoutes(allNavMenus)

// 缓存匹配结果，提高性能
const routeCache = new Map<string, Route>()

// 根据实际路径匹配对应的 Route 配置
export const findRouteByPath = (path: string): Route | undefined => {
  if (routeCache.has(path)) {
    return routeCache.get(path)
  }

  const found = flatRouteList.find(route => {
    const matcher = match(route.path, { decode: decodeURIComponent })
    return matcher(path) !== false
  })

  if (found) {
    routeCache.set(path, found)
  }

  return found
}
