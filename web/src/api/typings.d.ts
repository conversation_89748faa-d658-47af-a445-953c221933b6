declare namespace API {
  type AppCreateRequest = {
    /** Name */
    name: string
    /** Description */
    description?: string | null
    /** Icon Type */
    icon_type: string
    /** Icon */
    icon: string
    /** Icon Background */
    icon_background: string | null
    /** Tags */
    tags?: string[] | null
    /** Mode */
    mode: string
  }

  type AppResponse = {
    /** Name */
    name: string
    /** Description */
    description?: string | null
    /** Icon Type */
    icon_type: string
    /** Icon */
    icon: string
    /** Icon Background */
    icon_background: string | null
    /** Tags */
    tags?: string[] | null
    /** Id */
    id: string
    /** Type */
    type: string
    /** Mode */
    mode: string
    /** Status */
    status: string
    /** Created By */
    created_by?: string | null
    /** Created At */
    created_at: string
    /** Updated By */
    updated_by?: string | null
    /** Updated At */
    updated_at: string
  }

  type AppUpdateRequest = {
    /** Name */
    name: string
    /** Description */
    description?: string | null
    /** Icon Type */
    icon_type: string
    /** Icon */
    icon: string
    /** Icon Background */
    icon_background: string | null
    /** Tags */
    tags?: string[] | null
  }

  type BodyReorderProvider = {
    /** Provider Id */
    provider_id: string
    /** Target Provider Id */
    target_provider_id: string
  }

  type BodyUploadFile = {
    /** File */
    file: string
  }

  type callHostedMcpServerToolParams = {
    server_id: string
  }

  type ChatbotAppConfigGenerateRequest = {
    /** Idea */
    idea: string
  }

  type ChatbotAppConfigGenerateResponse = {
    /** Name */
    name: string
    /** Description */
    description: string
    /** Prompt */
    prompt: string
    /** Greeting */
    greeting?: string | null
    /** Preset Questions */
    preset_questions?: string[] | null
  }

  type ChatbotAppCreateRequest = {
    /** Name */
    name: string
    /** Description */
    description?: string | null
    /** Icon Type */
    icon_type: string
    /** Icon */
    icon: string
    /** Icon Background */
    icon_background: string | null
    /** Tags */
    tags?: string[] | null
    /** App Id */
    app_id: string
    /** Model */
    model?: Record<string, any> | null
    /** Chat */
    chat?: Record<string, any> | null
    /** Knowledge Base */
    knowledge_base?: Record<string, any> | null
    /** Database */
    database?: Record<string, any> | null
    /** Feature */
    feature?: Record<string, any> | null
    /** Plugin */
    plugin?: Record<string, any> | null
    /** Status */
    status: string
  }

  type ChatbotAppResponse = {
    /** App Id */
    app_id: string
    /** Model */
    model?: Record<string, any> | null
    /** Chat */
    chat?: Record<string, any> | null
    /** Knowledge Base */
    knowledge_base?: Record<string, any> | null
    /** Database */
    database?: Record<string, any> | null
    /** Feature */
    feature?: Record<string, any> | null
    /** Plugin */
    plugin?: Record<string, any> | null
    /** Status */
    status: string
    /** Id */
    id?: string | null
    app?: AppResponse | null
    /** Created At */
    created_at: string
    /** Updated At */
    updated_at: string
  }

  type ChatFile = {
    /** Type */
    type: 'document' | 'image'
    /** Mode */
    mode: 'remote' | 'local'
    /** Url */
    url: string | null
    /** Upload File Id */
    upload_file_id: string | null
  }

  type ChatRequest = {
    /** App Id */
    app_id: string
    /** Conversation Id */
    conversation_id?: string | null
    /** Question */
    question: string
    /** Model Id */
    model_id?: string | null
    /** Model Configs */
    model_configs?: Record<string, any> | null
    /** Inputs */
    inputs?: Record<string, any> | null
    /** Files */
    files?: ChatFile[] | null
    /** Stream */
    stream?: boolean | null
    /** Mcp Servers */
    mcp_servers?: string[] | null
  }

  type ChunkConfig = {
    /** Method 分块方式：fixed_length(固定长度)、by_paragraph(按段落)、custom(自定义) */
    method: string
    /** Size 分块长度，仅适用于fixed_length */
    size?: number | null
    /** Overlap 重叠部分，可选，提高上下文连贯性 */
    overlap?: number | null
    /** Custom Delimiters 自定义分隔符，适用于custom */
    custom_delimiters?: string[] | null
    /** Min Segment Size 最小分块长度，防止过小的分块 */
    min_segment_size?: number | null
  }

  type connectHostedMcpServerParams = {
    server_id: string
  }

  type ConversationCreate = {
    /** App Id */
    app_id: string
    /** Name */
    name: string
    /** Model Id */
    model_id?: string | null
    /** Model Configs */
    model_configs?: string | null
    /** System Instruction */
    system_instruction?: string | null
    /** Inputs */
    inputs?: Record<string, any> | null
  }

  type ConversationResponse = {
    /** App Id */
    app_id: string
    /** Name */
    name: string
    /** Model Id */
    model_id?: string | null
    /** Model Configs */
    model_configs?: string | null
    /** System Instruction */
    system_instruction?: string | null
    /** Inputs */
    inputs?: Record<string, any> | null
    /** Id */
    id: string
    /** Summary */
    summary?: string | null
    /** Status */
    status: string
    /** Created At */
    created_at: string
    /** Updated At */
    updated_at: string
  }

  type ConversationUpdate = {
    /** Name */
    name?: string | null
    /** Auto Generate */
    auto_generate?: boolean | null
    /** Model Id */
    model_id?: string | null
    /** Model Configs */
    model_configs?: Record<string, any> | null
    /** System Instruction */
    system_instruction?: string | null
    /** Inputs */
    inputs?: Record<string, any> | null
    /** Summary */
    summary?: string | null
  }

  type createDocumentParams = {
    kb_id: string
  }

  type createDocumentSegmentParams = {
    kb_id: string
    doc_id: string
  }

  type CreationRecordResponse = {
    /** Id */
    id: string
    /** Provider */
    provider: string
    /** Model */
    model: string
    /** Prompt */
    prompt: string
    /** Negative Prompt */
    negative_prompt: string
    /** Parameters */
    parameters?: Record<string, any>
    /** Status */
    status?: string
    /** Results */
    results?: CreationResultResponse[] | null
    /** Create At */
    create_at?: string
  }

  type CreationResultResponse = {
    /** Url */
    url: string | null
    /** Detail */
    detail?: Record<string, any> | null
  }

  type deleteAllConversationsParams = {
    app_id?: string | null
  }

  type deleteAppParams = {
    app_id: string
  }

  type deleteConversationParams = {
    conversation_id: string
  }

  type deleteDocumentParams = {
    kb_id: string
    doc_id: string
  }

  type deleteDocumentSegmentParams = {
    kb_id: string
    doc_id: string
    segment_id: string
  }

  type deleteFileParams = {
    file_id: string
  }

  type deleteHostedMcpServerParams = {
    server_id: string
  }

  type deleteKnowledgeBaseParams = {
    kb_id: string
  }

  type deleteModelParams = {
    model_id: string
  }

  type deleteProviderParams = {
    provider_id: string
  }

  type deleteVectorDatabaseParams = {
    db_id: string
  }

  type disconnectHostedMcpServerParams = {
    server_id: string
  }

  type DocumentCreateRequest = {
    /** Datasource 数据源信息 */
    datasource: DocumentSource[]
    /** Embedding Model 向量模型 */
    embedding_model?: string | null
    /** Embedding Model Provider 向量模型提供商 */
    embedding_model_provider?: string | null
    /** 检索配置 */
    retrieval_config?: RetrievalConfig | null
    /** 文档处理规则 */
    process_rule?: DocumentProcessRuleUpdateRequest | null
  }

  type DocumentError = {
    /** Stage 错误发生的阶段：sync、parse、preprocess、segment、index等 */
    stage: string
    /** Detail 错误的详细信息 */
    detail: string
  }

  type DocumentProcessRuleUpdateRequest = {
    /** 分块规则配置 */
    segment?: ChunkConfig | null
    /** 预处理规则配置 */
    preprocess?: PreprocessConfig | null
  }

  type DocumentResponse = {
    /** Kb Id 知识库ID */
    kb_id: string
    /** Process Rule Id 文档处理规则ID */
    process_rule_id: string
    /** Name 文档名称 */
    name: string
    /** 数据源信息 */
    source: DocumentSource
    /** Metadata 文档元数据 */
    metadata?: Record<string, any> | null
    /** Enabled 启用状态 */
    enabled?: boolean
    /** Id 文档ID */
    id: string
    /** Tenant Id 租户ID */
    tenant_id: string
    /** Word Count 文档字数 */
    word_count?: number | null
    /** Tokens 文档token数量 */
    tokens?: number | null
    /** Hit Count 命中次数 */
    hit_count?: number
    /** Status 状态：syncing、parsing、preprocessing、segmenting、waiting、indexing、error、completed、archived */
    status: string
    /** 错误信息 */
    error?: DocumentError | null
  }

  type DocumentSegmentCreateRequest = {
    /** Kb Id 知识库ID */
    kb_id: string
    /** Doc Id 文档ID */
    doc_id: string
    /** Position 分块在文档中的位置 */
    position: number
    /** Title 分块标题 */
    title: string
    /** Content 分块内容 */
    content: string
    /** Word Count 分块字数 */
    word_count: number
    /** Tokens 分块token数量 */
    tokens: number
    /** Keywords 关键词 */
    keywords?: string[] | null
    /** Enabled 是否启用 */
    enabled?: boolean
  }

  type DocumentSegmentResponse = {
    /** Kb Id 知识库ID */
    kb_id: string
    /** Doc Id 文档ID */
    doc_id: string
    /** Position 分块在文档中的位置 */
    position: number
    /** Title 分块标题 */
    title: string
    /** Content 分块内容 */
    content: string
    /** Word Count 分块字数 */
    word_count: number
    /** Tokens 分块token数量 */
    tokens: number
    /** Keywords 关键词 */
    keywords?: string[] | null
    /** Enabled 是否启用 */
    enabled?: boolean
    /** Id 分块ID */
    id: string
    /** Tenant Id 租户ID */
    tenant_id: string
    /** Index Node Id 索引节点ID */
    index_node_id?: string | null
    /** Index Node Hash 索引节点哈希值 */
    index_node_hash?: string | null
    /** Hit Count 命中次数 */
    hit_count?: number
    /** Status 状态：waiting、indexing、error、completed */
    status?: string
    /** 错误信息 */
    error?: DocumentError | null
    /** Indexing At 索引处理时间 */
    indexing_at?: string | null
    /** Completed At 完成时间 */
    completed_at?: string | null
    /** Stopped At 终止时间 */
    stopped_at?: string | null
  }

  type DocumentSegmentUpdateRequest = {
    /** Title 分块标题 */
    title?: string | null
    /** Content 分块内容 */
    content?: string | null
    /** Keywords 关键词 */
    keywords?: string[] | null
    /** Enabled 是否启用 */
    enabled?: boolean | null
  }

  type DocumentSource = {
    /** Type 数据源类型：file_upload(文件上传)、web_crawl(网页爬取)等 */
    type: string
    /** Details 数据源详细信息 */
    details: Record<string, any>
  }

  type DocumentUpdateRequest = {
    /** Id 文档ID */
    id: string
    /** Name 文档名称 */
    name?: string | null
    /** Metadata 文档元数据 */
    metadata?: Record<string, any> | null
    /** Enabled 启用状态 */
    enabled?: boolean | null
  }

  type FilePreviewResponse = {
    /** File Id */
    file_id: string
    /** Content */
    content: string
    /** Is Truncated */
    is_truncated: boolean
    /** Content Type */
    content_type: string
  }

  type FileResponse = {
    /** Name */
    name: string
    /** Size */
    size: number
    /** Extension */
    extension: string
    /** Mime Type */
    mime_type?: string | null
    /** Id */
    id: string
    /** Tenant Id */
    tenant_id: string
    /** Storage Type */
    storage_type: string
    /** Key */
    key: string
    /** Hash */
    hash?: string | null
    /** Created At */
    created_at: string
    /** Updated At */
    updated_at?: string | null
  }

  type generateSuggestionQuestionParams = {
    conversation_id: string
  }

  type getAppParams = {
    app_id: string
  }

  type getChatbotAppParams = {
    app_id: string
  }

  type getConversationParams = {
    conversation_id: string
  }

  type getCreationRecordParams = {
    record_id: string
  }

  type getDocumentParams = {
    kb_id: string
    doc_id: string
  }

  type getDocumentSegmentParams = {
    kb_id: string
    doc_id: string
    segment_id: string
  }

  type getFileParams = {
    file_id: string
  }

  type getHostedMcpServerParams = {
    server_id: string
  }

  type getKnowledgeBaseParams = {
    kb_id: string
  }

  type getMcpServerParams = {
    server_id: string
  }

  type getModelParams = {
    model_id: string
  }

  type getProviderParams = {
    provider_id: string
  }

  type getVectorDatabaseParams = {
    db_id: string
  }

  type HostedMcpServerAddFromManualRequest = {
    /** Name 名称 */
    name: string
    /** Icon 图标 */
    icon?: string | null
    /** Description 描述 */
    description?: string | null
    /** Server Name 服务名称 */
    server_name: string
    /** Transport Type 传输方式（sse、stdio） */
    transport_type: string
    /** Env 环境变量配置 */
    env?: Record<string, any> | null
    /** Url SSE 服务器 URL */
    url?: string | null
    /** Headers HTTP 请求头配置 */
    headers?: Record<string, any> | null
    /** Command 命令 */
    command?: string | null
    /** Args 命令行参数配置 */
    args?: string[] | null
  }

  type HostedMcpServerAddFromMarketRequest = {
    /** Server Id MCP Server ID，从 MCP Server 市场获取 */
    server_id: string
    /** Transport Type 传输方式（sse、stdio） */
    transport_type: string
    /** Type 部署类型（npx、uvx、docker、python） */
    type?: string | null
    /** Deploy Type 部署方式（local、remote） */
    deploy_type?: string | null
    /** Env 环境变量配置 */
    env?: Record<string, any> | null
    /** Args 命令行参数配置 */
    args?: Record<string, any> | null
  }

  type HostedMcpServerResponse = {
    /** Id ID */
    id: string
    /** Mcp Server Id MCP Server ID */
    mcp_server_id?: string | null
    /** Icon 图标 */
    icon?: string | null
    /** Name 名称 */
    name: string
    /** Type 部署类型（npx、uvx、docker、python） */
    type?: string | null
    /** Deploy Type 部署方式（local、remote） */
    deploy_type: string
    /** Server Name 服务名称 */
    server_name: string
    /** Transport Type 传输方式（sse、stdio） */
    transport_type: string
    /** Config 服务配置 */
    config: Record<string, any>
    /** Runtime 运行信息 */
    runtime: Record<string, any>
    /** Description 描述 */
    description?: string | null
    /** Tools 工具列表 */
    tools?: Record<string, any>[] | null
    /** Status 状态 */
    status: string
  }

  type HostedMcpServerStatsResponse = {
    /** Total */
    total: number
    /** Running */
    running: number
    /** Stopped */
    stopped: number
  }

  type HostedMcpServerToolCallRequest = {
    /** Tool Name 工具名称 */
    tool_name: string
    /** Arguments 参数 */
    arguments?: Record<string, any> | null
  }

  type HostedMcpServerUpdateRequest = {
    /** Name 名称 */
    name: string
    /** Icon 图标 */
    icon?: string | null
    /** Description 描述 */
    description?: string | null
    /** Server Name 服务名称 */
    server_name: string
    /** Transport Type 传输方式（sse、stdio） */
    transport_type: string
    /** Env 环境变量配置 */
    env?: Record<string, any> | null
    /** Headers HTTP 请求头配置 */
    headers?: Record<string, any> | null
    /** Url SSE 服务器 URL */
    url?: string | null
    /** Command 命令 */
    command?: string | null
    /** Args 命令行参数配置 */
    args?: any[] | null
  }

  type HTTPValidationError = {
    /** Detail */
    detail?: ValidationError[]
  }

  type ImageGenerateRequest = {
    /** Provider */
    provider: string
    /** Model */
    model: string
    /** Prompt */
    prompt: string
    /** Negative Prompt */
    negative_prompt?: string
    /** Size */
    size?: string
    /** Seed */
    seed?: number
    /** Guidance Scale */
    guidance_scale?: number
    /** Steps */
    steps?: number
    /** Batch Size */
    batch_size?: number
    /** Image */
    image?: string
  }

  type KnowledgeBaseCreateRequest = {
    /** Name 知识库名称 */
    name: string
    /** Description 知识库描述 */
    description?: string | null
    /** Icon 知识库图标 */
    icon?: string | null
    /** Vector Db Id 向量数据库ID */
    vector_db_id: string
  }

  type KnowledgeBaseResponse = {
    /** Name 知识库名称 */
    name: string
    /** Description 知识库描述 */
    description?: string | null
    /** Icon 知识库图标 */
    icon?: string | null
    /** Datasource Type 数据源类型 */
    datasource_type?: string | null
    /** Vector Db Id 向量数据库ID */
    vector_db_id: string
    /** Embedding Model 向量模型 */
    embedding_model?: string | null
    /** Embedding Model Provider 向量模型提供商 */
    embedding_model_provider?: string | null
    /** 检索配置 */
    retrieval_config?: RetrievalConfig | null
    /** Tags 标签 */
    tags?: string[] | null
    /** Id 知识库ID */
    id: string
    /** Tenant Id 租户ID */
    tenant_id: string
  }

  type KnowledgeBaseUpdateRequest = {
    /** Name 知识库名称 */
    name?: string | null
    /** Description 知识库描述 */
    description?: string | null
    /** Icon 知识库图标 */
    icon?: string | null
    /** Embedding Model 向量模型 */
    embedding_model?: string | null
    /** Embedding Model Provider 向量模型提供商 */
    embedding_model_provider?: string | null
    /** 检索配置 */
    retrieval_config?: RetrievalConfig | null
    /** Tags 标签 */
    tags?: string[] | null
  }

  type listAppsParams = {
    name?: string | null
    tags?: string[]
    mode?: string | null
    owner?: string | null
    page?: number
    page_size?: number
  }

  type listConversationParams = {
    /** 应用ID */
    app_id?: string | null
    /** 会话名称 */
    name?: string | null
    /** 每页数量 */
    limit?: number
    /** 上一页最后一条记录的ID */
    last_id?: string | null
  }

  type listDocumentParams = {
    kb_id: string
    enabled?: boolean | null
    status?: string | null
  }

  type listDocumentSegmentParams = {
    kb_id: string
    doc_id: string
    enabled?: boolean | null
    status?: string | null
  }

  type listFileParams = {
    tenant_id?: string | null
    name?: string | null
    page?: number
    page_size?: number
  }

  type listHostedMcpServerParams = {
    name?: string | null
    mcp_server_id?: string | null
    tags?: string[]
    deploy_type?: string | null
    status?: string | null
    page?: number
    page_size?: number
  }

  type listKnowledgeBaseParams = {
    name?: string | null
    datasource_type?: string | null
  }

  type listMcpServerParams = {
    name?: string | null
    tags?: string[]
    deploy_type?: string | null
    page?: number
    page_size?: number
  }

  type listMessageParams = {
    /** 会话ID */
    conversation_id: string
    /** 每页数量 */
    limit?: number
    /** 上一条消息ID */
    before_id?: string | null
  }

  type listModelParams = {
    name?: string
    types?: string[] | null
    features?: string[] | null
    provider_id?: string
  }

  type listProviderParams = {
    name?: string
    type?: string
    enabled?: boolean
  }

  type listVectorDatabaseParams = {
    type?: string | null
    name?: string | null
  }

  type LoginRequest = {
    /** Username */
    username: string
    /** Password */
    password: string
  }

  type LoginResponse = {
    /** Access Token */
    access_token: string
    /** Token Type */
    token_type: string
  }

  type McpServerBaseResponse = {
    /** Id ID */
    id: string
    /** Icon 图标 */
    icon?: string | null
    /** Name 名称 */
    name: string
    /** Category 类别 */
    category?: string | null
    /** Provider 提供者 */
    provider?: string | null
    /** Support Deploy Type 支持的部署方式（local、remote） */
    support_deploy_type: string[]
    /** Description 描述 */
    description?: string | null
    /** Tags 标签 */
    tags?: string[] | null
    /** Status 状态 */
    status: string
    /** Source 来源信息 */
    source?: Record<string, any> | null
    /** Hosted Id 已部署的服务 ID */
    hosted_id?: string | null
  }

  type McpServerResponse = {
    /** Id ID */
    id: string
    /** Icon 图标 */
    icon?: string | null
    /** Name 名称 */
    name: string
    /** Category 类别 */
    category?: string | null
    /** Provider 提供者 */
    provider?: string | null
    /** Support Deploy Type 支持的部署方式（local、remote） */
    support_deploy_type: string[]
    /** Description 描述 */
    description?: string | null
    /** Tags 标签 */
    tags?: string[] | null
    /** Status 状态 */
    status: string
    /** Source 来源信息 */
    source?: Record<string, any> | null
    /** Hosted Id 已部署的服务 ID */
    hosted_id?: string | null
    /** Config 服务配置 */
    config: Record<string, any>[]
    /** Tools 工具列表 */
    tools?: Record<string, any>[] | null
    /** Readme README，服务详情等信息 */
    readme?: string | null
    /** Readme Cn README，服务详情等信息 */
    readme_cn?: string | null
  }

  type McpServerUpdateRequest = {
    /** Icon 图标 */
    icon?: string | null
    /** Name 名称 */
    name: string
    /** Category 类别 */
    category?: string | null
    /** Provider 提供者 */
    provider?: string | null
    /** Support Deploy Type 支持的部署方式（local、remote） */
    support_deploy_type: string[]
    /** Description 描述 */
    description?: string | null
    /** Tags 标签 */
    tags?: string[] | null
    /** Status 状态 */
    status: string
    /** Source 来源信息 */
    source?: Record<string, any> | null
    /** Config 服务配置 */
    config: Record<string, any>[]
    /** Readme README，服务详情等信息 */
    readme?: string | null
    /** Readme Cn README，服务详情等信息 */
    readme_cn?: string | null
  }

  type MessageFileResponse = {
    /** Type */
    type: string
    /** Mode */
    mode: string
    /** Url */
    url?: string | null
    /** Upload File Id */
    upload_file_id?: string | null
    /** Name */
    name?: string | null
    /** Size */
    size?: number | null
  }

  type MessageResponse = {
    /** Id */
    id: string
    /** Conversation Id */
    conversation_id: string
    /** App Id */
    app_id: string
    /** Parent Id */
    parent_id?: string | null
    model: Model
    /** Model Configs */
    model_configs?: Record<string, any> | null
    /** Role */
    role: string
    /** Type */
    type: string
    /** Reasoning Content */
    reasoning_content?: string | null
    /** Content */
    content?: string | null
    /** Token */
    token?: number | null
    /** Message Metadata */
    message_metadata?: Record<string, any> | null
    /** Feedback */
    feedback?: string | null
    /** Status */
    status: string
    /** Error */
    error?: string | null
    /** Created At */
    created_at: number
    /** Updated At */
    updated_at: number
    /** Files */
    files?: MessageFileResponse[] | null
    /** Tool Calls */
    tool_calls?: MessageToolCallResponse[] | null
  }

  type MessageToolCallResponse = {
    /** Id */
    id: string
    /** Message Id */
    message_id: string
    /** Tool Name */
    tool_name: string
    /** Server Name */
    server_name?: string | null
    /** Arguments */
    arguments?: Record<string, any> | null
    /** Result */
    result?: Record<string, any> | null
    /** Status */
    status?: string
    /** Error */
    error?: string | null
    /** Started At */
    started_at?: string | null
    /** Ended At */
    ended_at?: string | null
  }

  type Model = {
    /** Id */
    id: string
    /** Model Id */
    model_id?: string | null
    /** Model Name */
    model_name?: string | null
    /** Provider Id */
    provider_id?: string | null
    /** Provider Name */
    provider_name?: string | null
  }

  type ModelCreateRequest = {
    /** Name */
    name: string
    /** Model Id */
    model_id: string
    /** Type */
    type: string
    /** Description */
    description?: string | null
    /** Max Tokens */
    max_tokens?: number | null
    /** Features */
    features?: string[] | null
    /** Config */
    config?: Record<string, any> | null
    /** Group */
    group?: string | null
    /** Base Url */
    base_url?: string | null
    /** Api Key */
    api_key?: string | null
    /** Provider Id */
    provider_id: string
  }

  type ModelResponse = {
    /** Name */
    name: string
    /** Model Id */
    model_id: string
    /** Type */
    type: string
    /** Description */
    description?: string | null
    /** Max Tokens */
    max_tokens?: number | null
    /** Features */
    features?: string[] | null
    /** Config */
    config?: Record<string, any> | null
    /** Group */
    group?: string | null
    /** Base Url */
    base_url?: string | null
    /** Api Key */
    api_key?: string | null
    /** Id */
    id: string
    /** Provider Id */
    provider_id: string
    /** Provider Name */
    provider_name?: string | null
  }

  type ModelUpdateRequest = {
    /** Name */
    name?: string | null
    /** Model Id */
    model_id?: string | null
    /** Type */
    type?: string | null
    /** Description */
    description?: string | null
    /** Max Tokens */
    max_tokens?: number | null
    /** Features */
    features?: string[] | null
    /** Config */
    config?: Record<string, any> | null
    /** Group */
    group?: string | null
    /** Base Url */
    base_url?: string | null
    /** Api Key */
    api_key?: string | null
    /** Enabled */
    enabled?: boolean | null
    /** Provider Id */
    provider_id?: string | null
  }

  type PageAppResponse_ = {
    /** Items */
    items: AppResponse[]
    /** Total */
    total: number
    /** Page */
    page: number
    /** Page Size */
    page_size: number
  }

  type PageHostedMcpServerResponse_ = {
    /** Items */
    items: HostedMcpServerResponse[]
    /** Total */
    total: number
    /** Page */
    page: number
    /** Page Size */
    page_size: number
  }

  type PageMcpServerBaseResponse_ = {
    /** Items */
    items: McpServerBaseResponse[]
    /** Total */
    total: number
    /** Page */
    page: number
    /** Page Size */
    page_size: number
  }

  type PreprocessConfig = {
    /** Cleaning 清洗配置，如移除HTML标签、特殊字符等 */
    cleaning?: Record<string, any> | null
    /** Normalization 标准化配置，如转小写、去除多余空格等 */
    normalization?: Record<string, any> | null
    /** Filter 过滤配置，如过滤过短或过长的文本 */
    filter?: Record<string, any> | null
  }

  type previewFileContentParams = {
    file_id: string
  }

  type previewFileParams = {
    file_id: string
  }

  type ProviderCreateRequest = {
    /** Id */
    id: string
    /** Name */
    name: string
    /** Type */
    type: string
    /** Base Url */
    base_url: string
    /** Api Key */
    api_key?: string | null
    /** Enabled */
    enabled?: boolean
    /** Is Valid */
    is_valid?: boolean | null
    /** Sort Order */
    sort_order?: number | null
  }

  type ProviderResponse = {
    /** Id */
    id: string
    /** Name */
    name: string
    /** Type */
    type: string
    /** Base Url */
    base_url: string
    /** Api Key */
    api_key?: string | null
    /** Enabled */
    enabled?: boolean
    /** Is Valid */
    is_valid?: boolean | null
    /** Sort Order */
    sort_order?: number | null
    /** Config */
    config?: Record<string, any> | null
  }

  type ProviderUpdateRequest = {
    /** Name */
    name?: string | null
    /** Type */
    type?: string | null
    /** Base Url */
    base_url?: string | null
    /** Api Key */
    api_key?: string | null
    /** Enabled */
    enabled?: boolean | null
    /** Sort Order */
    sort_order?: number | null
  }

  type publishAppParams = {
    app_id: string
  }

  type RetrievalConfig = {
    /** Mode 检索模式：向量(vector)、全文(fulltext)、混合(hybrid) */
    mode: string
    /** Vector Weight 向量检索权重，混合检索时生效 */
    vector_weight?: number | null
    /** Fulltext Weight 全文检索权重，混合检索时生效 */
    fulltext_weight?: number | null
    /** Top K 返回结果数量 */
    top_k?: number
    /** Score Threshold 相似度阈值 */
    score_threshold?: number
    /** Rerank Model 重排序模型 */
    rerank_model?: string | null
    /** Rerank Provider 重排序模型提供商 */
    rerank_provider?: string | null
  }

  type stopChatMessagesParams = {
    conversation_id: string
  }

  type updateAppParams = {
    app_id: string
  }

  type updateConversationParams = {
    conversation_id: string
  }

  type updateDocumentParams = {
    kb_id: string
    doc_id: string
  }

  type updateDocumentSegmentParams = {
    kb_id: string
    doc_id: string
    segment_id: string
  }

  type updateHostedMcpServerParams = {
    server_id: string
  }

  type updateKnowledgeBaseParams = {
    kb_id: string
  }

  type updateMcpServerParams = {
    server_id: string
  }

  type updateModelParams = {
    model_id: string
  }

  type updateProviderParams = {
    provider_id: string
  }

  type updateVectorDatabaseParams = {
    db_id: string
  }

  type ValidationError = {
    /** Location */
    loc: (string | number)[]
    /** Message */
    msg: string
    /** Error Type */
    type: string
  }

  type VectorDatabaseCreateRequest = {
    /** Name 向量数据库名称 */
    name: string
    /** Mode 服务模式 */
    mode: string
    /** Type 向量数据库类型，如chroma、faiss等 */
    type: string
    /** Config 向量数据库配置信息 */
    config: Record<string, any>
  }

  type VectorDatabaseResponse = {
    /** Name 向量数据库名称 */
    name: string
    /** Mode 服务模式 */
    mode: string
    /** Type 向量数据库类型，如chroma、faiss等 */
    type: string
    /** Config 向量数据库配置信息 */
    config: Record<string, any>
    /** Id 向量数据库ID */
    id: string
  }

  type VectorDatabaseUpdateRequest = {
    /** Name 向量数据库名称 */
    name?: string | null
    /** Config 向量数据库配置信息 */
    config?: Record<string, any> | null
  }
}
