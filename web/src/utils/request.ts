import axios, { AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios'
import { config } from '../config/env'

// 自定义错误类型
interface CustomError extends Error {
  status?: number
  data?: any
}

// 定义API错误响应结构
interface ApiErrorResponse {
  message?: string
  code?: number
  data?: any
}

// 创建一个事件总线用于跨组件通信
export const authEvents = {
  // 触发401未授权事件
  emit401Error: () => {
    const event = new CustomEvent('auth:unauthorized')
    window.dispatchEvent(event)
  },
  // 监听401未授权事件
  on401Error: (callback: () => void) => {
    window.addEventListener('auth:unauthorized', callback)
  },
  // 移除401未授权事件监听
  off401Error: (callback: () => void) => {
    window.removeEventListener('auth:unauthorized', callback)
  },
}

// 创建 axios 实例
const request = axios.create({
  baseURL: config.API_BASE_URL,
  timeout: 60000,
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')

    // 如果有token则使用，否则不设置Authorization头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 如果URL包含 /files/upload ，则不设置 Content-Type 为 application/json
    if (!config.url?.includes('/files/upload')) {
      config.headers['Content-Type'] = 'application/json'
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const contentType = response.headers['content-type']

    // 如果是 SSE 响应，直接返回原始 response
    if (contentType && contentType.includes('text/event-stream')) {
      return response
    }

    // 如果是 Blob 类型的响应（如图片）
    if (
      contentType &&
      (contentType.includes('image') || contentType.includes('application/octet-stream'))
    ) {
      return response.data
    }

    // 如果是文本响应
    if (contentType && contentType.includes('text/')) {
      return response.data
    }

    // 默认返回 JSON 数据
    return response.data
  },
  (error: AxiosError) => {
    // 检查是否是401错误
    if (error.response?.status === 401) {
      authEvents.emit401Error()

      const customError = new Error('未授权，请登录') as CustomError
      customError.status = 401
      customError.data = error.response?.data
      return Promise.reject(customError)
    }

    // 处理其他HTTP错误
    if (error.response) {
      const responseData = error.response.data as ApiErrorResponse
      const customError = new Error(
        responseData?.message || error.message || '服务器错误'
      ) as CustomError
      customError.status = error.response.status
      customError.data = responseData
      return Promise.reject(customError)
    }

    // 处理网络错误
    if (error.request) {
      const customError = new Error('网络错误，请检查网络连接') as CustomError
      return Promise.reject(customError)
    }

    // 处理其他错误
    return Promise.reject(error)
  }
)

export default request
