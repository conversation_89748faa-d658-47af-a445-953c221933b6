import { ModelIcon, modelMappings, ProviderIcon } from '@lobehub/icons'
import { Meta, AiHubMix, Infinigence } from '@lobehub/icons'

import { JSX, ReactElement } from 'react'

// 支持深度思考模型匹配规则
export const REASONING_REGEX =
  /^(o\d+(?:-[\w-]+)?|.*\b(?:reasoner|thinking)\b.*|.*-[rR]\d+.*|.*\bqwq(?:-[\w-]+)?\b.*|.*\bqwen3(?:-[\w-]+)?\b.*)$/i

export const isReasoningModel = (model?: any) => {
  if (!model) {
    return false
  }

  return REASONING_REGEX.test(model.id) || model.type?.includes('reasoning') || false
}

// Vision 模型匹配规则
const supportVisionModels = [
  'llava',
  'moondream',
  'minicpm',
  'gemini-1\\.5',
  'gemini-2\\.0',
  'gemini-exp',
  'claude-3',
  'vision',
  'glm-4v',
  'qwen-vl',
  'qwen2-vl',
  'qwen2.5-vl',
  'internvl2',
  'grok-vision-beta',
  'pixtral',
  'gpt-4(?:-[\\w-]+)',
  'gpt-4o(?:-[\\w-]+)?',
  'chatgpt-4o(?:-[\\w-]+)?',
  'o1(?:-[\\w-]+)?',
  'deepseek-vl(?:[\\w-]+)?',
  'kimi-latest',
]

const unsuppportVisionModels = [
  'gpt-4-\\d+-preview',
  'gpt-4-turbo-preview',
  'gpt-4-32k',
  'gpt-4-\\d+',
]

export const VISION_REGEX = new RegExp(
  `\\b(?!(?:${unsuppportVisionModels.join('|')})\\b)(${supportVisionModels.join('|')})\\b`,
  'i'
)

export const isVisionModel = (model?: any) => {
  if (!model) {
    return false
  }

  return VISION_REGEX.test(model.id) || model?.type?.includes('vision') || false
}

// Embedding 模型匹配规则
export const EMBEDDING_REGEX =
  /(?:^text-|embed|bge-|e5-|LLM2Vec|retrieval|uae-|gte-|jina-clip|jina-embeddings)/i

export const isEmbeddingModel = (model?: any) => {
  if (!model) {
    return false
  }

  return EMBEDDING_REGEX.test(model.id) || model.type?.includes('embedding') || false
}

// Rerank 模型匹配规则
export const RERANK_REGEX = /(?:^rerank-|rank|bge-reranker|sentence-reranker|colbert|dpr|bm25)/i

export const isRerankModel = (model?: any) => {
  if (!model) return false
  return RERANK_REGEX.test(model.id) || model.type?.includes('rerank') || false
}

// Video 模型匹配规则
const videoAllowedModels = [
  'video',
  'movie',
  'clip-video',
  'vision-video',
  'llava-video',
  'video-llava',
  'moviechat',
  'vila',
  'videochat',
  'pix2struct',
  'flamingo',
  'blip2',
  'instructblip',
  'minigpt4',
  'video-caption',
  'video-recognize',
]

export const VIDEO_REGEX = new RegExp(`\\b(${videoAllowedModels.join('|')})\\b`, 'i')

export const isVideoModel = (model?: any) => {
  if (!model) return false
  return VIDEO_REGEX.test(model.id) || model.type?.includes('video') || false
}

// Audio 模型匹配规则
const audioAllowedModels = [
  'audio',
  'sound',
  'voice',
  'music',
  'whisper',
  'speech',
  'asr',
  'tts',
  'wav2vec',
  'deepspeech',
  'audio-embed',
  'musicgen',
  'audiogen',
  'voice-cloning',
  'text-to-speech',
  'speech-to-text',
]

export const AUDIO_REGEX = new RegExp(`\\b(${audioAllowedModels.join('|')})\\b|.*voice.*`, 'i')

export const isAudioModel = (model?: any) => {
  if (!model) return false
  return AUDIO_REGEX.test(model.id) || model.type?.includes('audio') || false
}

// Image 模型匹配规则
const imageGenAllowedModels = [
  'stable-diffusion',
  'sd-xl',
  'sdxl',
  'kolors',
  'dall-e',
  'dalle',
  'midjourney',
  'kandinsky',
  'imagen',
  'deepfloyd',
  'stability-ai',
  'pixart',
  'wuyi',
  'cogvlm',
  'playground',
  'flux',
  'flux.1',
]

export const IMAGE_GEN_REGEX = new RegExp(
  `\\b(${imageGenAllowedModels.join(
    '|'
  )})\\b|\\b(?:stable-diffusion(?:-[\\w.-]+)?|flux\.1-(?:schnell|dev|pro))\\b`,
  'i'
)

export const isImageModel = (model?: any) => {
  if (!model) return false
  return IMAGE_GEN_REGEX.test(model.id) || model.type?.includes('image') || false
}

// 获取模型提供商图标
import CoresHub from '@/assets/icons/provider/coreshub.png'
import DashScope from '@/assets/icons/provider/dashscope.png'
// import AiHubMix from '@/assets/icons/provider/aihubmix.png'
import Hyperbolic from '@/assets/icons/provider/hyperbolic.svg'
import { Avatar } from 'antd'

const customProviderIcons: Record<string, string> = {
  coreshub: CoresHub,
  dashscope: DashScope,
  hyperbolic: Hyperbolic,
}

const specifyProviderIcons: Record<string, ReactElement> = {
  meta: <Meta.Avatar size={25} />,
  aihubmix: <AiHubMix.Avatar size={25} />,
  infini: <Infinigence.Avatar size={25} />,
}

export const getProviderIcon = (provider: string, size: number = 48) => {
  if (customProviderIcons[provider]) {
    return <Avatar src={<img src={customProviderIcons[provider]} />} size={size + 2} />
  }
  if (specifyProviderIcons[provider]) {
    return specifyProviderIcons[provider]
  }
  return <ProviderIcon provider={provider} size={size} type={'avatar'} />
}

// 创建关键词到图标的映射缓存
const modelIconCache = new Map<string, JSX.Element | null>()
const keywordMap = new Map<string, string>()

// 初始化关键词映射
Object.values(modelMappings).forEach(item => {
  item.keywords.forEach(keyword => {
    const cleanKeyword = keyword.replace('^', '')
    keywordMap.set(cleanKeyword.toLowerCase(), cleanKeyword)
  })
})

// 获取模型图标
export const getModelIcon = (modelId: string, size: number = 48, provider_id?: string | null) => {
  // console.log('getModelIcon', modelId, provider_id)
  const cacheKey = `${modelId}-${size}`

  // 检查缓存
  if (modelIconCache.has(cacheKey)) {
    return modelIconCache.get(cacheKey)
  }

  // 处理包含斜杠的模型ID
  console.log('modelId', modelId)
  const parts = modelId.split('/')
  const searchId = parts.length > 1 ? parts[1] : modelId
  const modelIdLower = searchId.toLowerCase()

  // 使用 Map 查找匹配的关键词
  for (const [keyword, cleanKeyword] of keywordMap) {
    if (modelIdLower.startsWith(keyword.toLowerCase())) {
      const icon = <ModelIcon key={cleanKeyword} model={cleanKeyword} size={size} />
      modelIconCache.set(cacheKey, icon)
      return icon
    }
  }

  // 如果没有找到匹配的图标且提供了 provider_id，则使用 ProviderIcon
  if (provider_id) {
    const providerIcon = <ProviderIcon provider={provider_id} size={size} type={'avatar'} />
    modelIconCache.set(cacheKey, providerIcon)
    return providerIcon
  }

  modelIconCache.set(cacheKey, null)
  return null
}
