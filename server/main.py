from fastapi import <PERSON><PERSON><PERSON>, APIRouter, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from starlette.responses import JSONResponse

from app.api import model, provider, conversation, chat, file, auth
from app.api.kb import knowledge_base, document, document_segment
from app.api.system import vector_database
from app.api.creation import image, creation
from app.api import mcp
from app.api import mcp_hosted
from app.api import app as application

from app.core.config import settings
from app.core.database import engine, get_db
from app.core.auth import AuthMiddleware

from server.app.models.mcp import HostedMcpServer
from server.app.services.mcp.mcp_manager import McpManager


# 创建数据库表
def init_database():
    # from app.models import model as model_tables
    #
    # model_tables.Base.metadata.create_all(bind=engine)
    #
    # from app.models import chat as chat_tables
    #
    # chat_tables.Base.metadata.create_all(bind=engine)
    #
    # from app.models import file as file_tables
    #
    # file_tables.Base.metadata.create_all(bind=engine)

    from app.models import mcp as mcp_tables

    mcp_tables.Base.metadata.create_all(bind=engine)
    # pass


init_database()

# 配置路由
api_router = APIRouter(prefix=settings.api_prefix)
# Auth
api_router.include_router(auth.router)
# Model
api_router.include_router(provider.router)
api_router.include_router(model.router)
# App
api_router.include_router(application.router)
# Chat
api_router.include_router(conversation.router)
api_router.include_router(chat.router)
api_router.include_router(file.router)
# Vector Database
api_router.include_router(vector_database.router)
# KnowledgeBase
api_router.include_router(knowledge_base.router)
api_router.include_router(document.router)
api_router.include_router(document_segment.router)
# Creation
api_router.include_router(image.router)
api_router.include_router(creation.router)
# Mcp Server
api_router.include_router(mcp.router)
api_router.include_router(mcp_hosted.router)


@asynccontextmanager
async def lifespan(app: FastAPI):
    app.openapi_schema = app.openapi()
    app.openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
        }
    }
    app.openapi_schema["security"] = [{"BearerAuth": []}]

    # 初始化管理器
    await init_mcp_manager(app)

    yield
    # 清理资源
    await app.state.mcp_manager.exit_stack.aclose()
    print("Application is shutting down")


async def init_mcp_manager(app):
    app.state.mcp_manager = McpManager()
    db_gen = get_db()  # 获取生成器
    db = next(db_gen)
    try:
        deployed_servers = db.query(HostedMcpServer).filter(HostedMcpServer.status == 'running').all()
        # reconnect mcp servers
        # if deployed_servers:
        #     servers = {}
        #     for item in deployed_servers:
        #         servers.update(item.config)
        #     await app.state.mcp_manager.initialize(servers)

        # update status
        for item in deployed_servers:
            item.status = 'stopped'
            # db.add(item)
        db.commit()
    finally:
        db.close()


app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    lifespan=lifespan,
    debug=settings.debug,
    openapi_url="/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",

)

# 配置路由
app.include_router(api_router)

# 配置认证中间件
app.add_middleware(AuthMiddleware)

# 配置 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_allow_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)


# 自定义异常处理器
@app.exception_handler(HTTPException)
def custom_http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"code": exc.status_code, "message": exc.detail, "data": None})
