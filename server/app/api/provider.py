from typing import List

from fastapi import APIRouter, Body, Depends, HTTPException
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..schemas.model import (
    ProviderCreateRequest,
    ProviderResponse,
    ProviderUpdateRequest,
)
from ..services.model_service import ModelService

router = APIRouter(tags=["Model Provider"])


@router.post(
    "/providers",
    response_model=ProviderResponse,
    summary="新增模型提供商",
    operation_id="add_provider",
)
def create_provider(provider: ProviderCreateRequest, db: Session = Depends(get_db)):
    return ModelService.create_provider(db, provider)


@router.get(
    "/providers",
    response_model=List[ProviderResponse],
    summary="获取模型提供商列表",
    operation_id="list_provider",
)
def list_provider(
    name: str = None,
    type: str = None,
    enabled: bool = None,
    db: Session = Depends(get_db),
):
    return ModelService.list_provider(db, name, type, enabled)


@router.put(
    "/providers/reorder",
    summary="重新排序模型提供商",
    operation_id="reorder_provider",
)
def reorder_provider(
    provider_id: str = Body(..., embed=True),
    target_provider_id: str = Body(..., embed=True),
    db: Session = Depends(get_db),
):
    """
    重新排序模型提供商 - 将一个提供商移动到另一个提供商之前
    """
    if ModelService.reorder_provider(db, provider_id, target_provider_id):
        return {"ok": True}
    raise HTTPException(status_code=400, detail="Reorder provider failed")


@router.get(
    "/providers/{provider_id}",
    response_model=ProviderResponse,
    summary="获取模型提供商详情",
    operation_id="get_provider",
)
def get_provider(provider_id: str, db: Session = Depends(get_db)):
    provider = ModelService.get_provider(db, provider_id)
    if provider is None:
        raise HTTPException(status_code=404, detail="Provider not found")
    return provider


@router.put(
    "/providers/{provider_id}",
    response_model=ProviderResponse,
    summary="更新模型提供商",
    operation_id="update_provider",
)
def update_provider(
    provider_id: str, provider: ProviderUpdateRequest, db: Session = Depends(get_db)
):
    updated_provider = ModelService.update_provider(db, provider_id, provider)
    if updated_provider is None:
        raise HTTPException(status_code=404, detail="Provider not found")
    return updated_provider


@router.delete(
    "/providers/{provider_id}", summary="删除模型提供商", operation_id="delete_provider"
)
def delete_provider(provider_id: str, db: Session = Depends(get_db)):
    if not ModelService.delete_provider(db, provider_id):
        raise HTTPException(status_code=404, detail="Provider not found")
    return {"ok": True}
