from typing import List

import httpx
from fastapi import HTTPException
from sqlalchemy.orm import Session

from ..models.model import Model, ModelProvider
from ..schemas.model import (
    ModelCreateRequest,
    ModelResponse,
    ModelUpdateRequest,
    ProviderCreateRequest,
    ProviderUpdateRequest,
)
from ..utils.security import get_security_service


class ModelService:
    @staticmethod
    def create_provider(db: Session, provider: ProviderCreateRequest):
        db_provider = ModelProvider(**provider.dict())
        db.add(db_provider)
        db.commit()
        db.refresh(db_provider)
        return db_provider

    @staticmethod
    def list_provider(
        db: Session, name: str = None, type: str = None, enabled: bool = None
    ):
        query = (
            db.query(ModelProvider)
            .filter(ModelProvider.deleted == False)
            .order_by(
                ModelProvider.sort_order.asc(), ModelProvider.name.asc()
            )  # 先按排序字段，再按名称排序
        )
        if name:
            query = query.filter(ModelProvider.name.ilike(f"%{name}%"))
        if type:
            query = query.filter(ModelProvider.type.ilike(f"%{type}%"))
        if enabled is not None:
            query = query.filter(ModelProvider.enabled == enabled)
        return query.all()

    @staticmethod
    def get_provider(db: Session, provider_id: str):
        return db.query(ModelProvider).filter(ModelProvider.id == provider_id).first()

    @staticmethod
    def update_provider(db: Session, provider_id: str, provider: ProviderUpdateRequest):
        db_provider = ModelService.get_provider(db, provider_id)
        if db_provider:
            for key, value in provider.dict().items():
                if type(value) == bool or value:
                    setattr(db_provider, key, value)
            db.commit()
            db.refresh(db_provider)
        return db_provider

    @staticmethod
    def delete_provider(db: Session, provider_id: str):
        db_provider = ModelService.get_provider(db, provider_id)
        if db_provider:
            db.delete(db_provider)
            db.commit()
            return True
        return False

    @staticmethod
    async def validate_model(base_url: str, api_key: str, model_id: str) -> bool:
        try:
            base_url = base_url.rstrip("/")
            url = f"{base_url}/models"

            print(
                f"[Model Validation] Starting validation for model_id: {model_id} with base_url: {base_url}"
            )
            print(f"[Model Validation] Using API key: {api_key}")

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            }
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                print(
                    f"[Model Validation] Received response with status code: {response.status_code}"
                )

                if response.status_code != 200:
                    error_msg = f"Model validation failed: API returned status code {response.status_code}"
                    print(f"[Model Validation] Error: {error_msg}")
                    raise HTTPException(status_code=400, detail=error_msg)
                data = response.json()
                models = data.get("data", []) if isinstance(data, dict) else data
                print(f"[Model Validation] Retrieved {len(models)} models from API")

                model_exists = any(model.get("id", "") == model_id for model in models)
                if not model_exists:
                    error_msg = f"Model {model_id} does not exist or is not accessible"
                    print(f"[Model Validation] Error: {error_msg}")
                    raise HTTPException(status_code=400, detail=error_msg)
                print(f"[Model Validation] Successfully validated model_id: {model_id}")
                return True

        except HTTPException as e:
            print(f"[Model Validation] HTTPException occurred: {str(e)}")
            raise e
        except Exception as e:
            error_msg = f"Model validation failed: {str(e)}"
            print(f"[Model Validation] Unexpected error: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)

    @staticmethod
    async def create_model(db: Session, model: ModelCreateRequest):
        if model.base_url:
            await ModelService.validate_model(
                model.base_url, model.api_key, model.model_id
            )
        db_model = Model(**model.dict())
        if db_model.api_key:
            db_model.api_key = get_security_service().encrypt(db_model.api_key)
        db.add(db_model)
        db.commit()
        db.refresh(db_model)
        return db_model

    @staticmethod
    def list_model(
        db: Session,
        name: str = None,
        types: List[str] = None,
        features: List[str] = None,
        provider_id: str = None,
    ) -> List[ModelResponse]:
        query = (
            db.query(Model, ModelProvider.name.label("provider_name"))
            .join(ModelProvider, Model.provider_id == ModelProvider.id)
            .filter(
                ModelProvider.enabled == True,
                # ModelProvider.is_valid == True
            )
        )

        if name:
            query = query.filter(
                (Model.name.ilike(f"%{name}%")) | (Model.model_id.ilike(f"%{name}%"))
            )
        if provider_id:
            query = query.filter(Model.provider_id == provider_id)
        if types:
            query = query.filter(Model.type.in_(types))
        if features:
            from sqlalchemy import or_

            feature_conditions = [
                Model.features.op("?")(feature) for feature in features
            ]
            query = query.filter(or_(*feature_conditions))

        results = query.all()
        items = []
        for model, provider_name in results:
            model_dict = ModelResponse.from_orm(model).dict()
            model_dict["provider_name"] = provider_name
            items.append(ModelResponse(**model_dict))

        return items

    @staticmethod
    def get_model_with_provider(db: Session, model_id: str):
        model, provider_name = (
            db.query(Model, ModelProvider.name.label("provider_name"))
            .join(ModelProvider, Model.provider_id == ModelProvider.id)
            .filter(Model.id == model_id)
            .first()
        )
        if model:
            model_dict = ModelResponse.from_orm(model).dict()
            model_dict["provider_name"] = provider_name
            return ModelResponse(**model_dict)
        return model

    @staticmethod
    def get_model(db: Session, model_id: str):
        return db.query(Model).filter(Model.id == model_id).first()

    @staticmethod
    async def update_model(db: Session, model_id: str, model: ModelUpdateRequest):
        db_model = ModelService.get_model(db, model_id)
        if db_model:
            if model.api_key:
                if db_model.api_key == model.api_key:
                    model.api_key = get_security_service().decrypt(model.api_key)
                    await ModelService.validate_model(
                        model.base_url, model.api_key, model.model_id
                    )
                else:
                    model.api_key = get_security_service().encrypt(model.api_key)
            for key, value in model.dict().items():
                setattr(db_model, key, value)
            db.commit()
            db.refresh(db_model)
        return db_model

    @staticmethod
    def delete_model(db: Session, model_id: str):
        db_model = ModelService.get_model(db, model_id)
        if db_model:
            db.delete(db_model)
            db.commit()
            return True
        return False

    @staticmethod
    def get_model_base_info_map(db: Session, model_ids: list[str]):
        result = (
            db.query(
                Model.id,
                Model.model_id,
                Model.name.label("model_name"),
                Model.provider_id,
                ModelProvider.name.label("provider_name"),
            )
            .join(ModelProvider, ModelProvider.id == Model.provider_id)
            .filter(Model.id.in_(model_ids))
            .all()
        )
        return {model.id: model for model in result}

    @staticmethod
    def reorder_provider(db: Session, provider_id: str, target_provider_id: str):
        """
        重新排序提供商 - 将一个提供商移动到另一个提供商之前

        Args:
            provider_id: 要移动的提供商ID
            target_provider_id: 目标位置提供商ID（移动到这个提供商之前）
        """
        # 获取要移动的提供商和目标提供商
        provider = (
            db.query(ModelProvider).filter(ModelProvider.id == provider_id).first()
        )
        target_provider = (
            db.query(ModelProvider)
            .filter(ModelProvider.id == target_provider_id)
            .first()
        )

        if not provider or not target_provider:
            return False

        # 获取目标提供商的排序值
        target_sort = target_provider.sort_order

        # 将所有大于等于目标排序值的提供商排序值加1000
        db.query(ModelProvider).filter(
            ModelProvider.sort_order >= target_sort, ModelProvider.id != provider_id
        ).update(
            {ModelProvider.sort_order: ModelProvider.sort_order + 1000},
            synchronize_session=False,
        )

        # 设置移动的提供商排序值为目标排序值
        provider.sort_order = target_sort

        db.commit()
        return True

    # @staticmethod
    # def get_model(db: Session, provider_id: str, model_id: str):
    #     result = (db.query(Model, ModelProvider)
    #              .join(ModelProvider, Model.provider_id == ModelProvider.id)
    #              .filter(Model.provider_id == provider_id)
    #              .filter(Model.model_id == model_id)
    #              .first())
    #
    #     if result:
    #         model, provider = result
    #         return {
    #             "id": model.id,
    #             "model_id": model.model_id,
    #             "model_name": model.name,
    #             "type": model.type,
    #             "features": model.features,
    #             "model_config": model.config,
    #             "provider_id": model.provider_id,
    #             "provider_name": provider.name,
    #             "provider_type": provider.type,
    #             "base_url": provider.base_url,
    #             "api_key": provider.api_key,
    #             "enabled": provider.enabled,
    #         }
    #     return None
